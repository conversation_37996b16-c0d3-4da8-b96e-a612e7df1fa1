name: Deploy Potions to PROD

on:
  workflow_dispatch:
    inputs:
      version:
        description: "Image version tag (default: latest)"
        required: false
        default: latest

jobs:
  deploy:
    runs-on: sonar
    timeout-minutes: 30
    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
      ECR_IMAGE_TAG: ${{ github.event.inputs.version }}
      ECR_REPOSITORY: ${{ vars.ECR_REPOSITORY }}
      ECS_CLUSTER: rozana-cluster-prod

      ECS_API_SERVICE: rozana-potions-api-prod-service
      ECS_API_TASK: rozana-potions-api-prod

      ECS_CELERY_SERVICE: rozana-potions-celery-prod-service
      ECS_CELERY_TASK: rozana-potions-celery-prod

      ECS_BEAT_SERVICE: rozana-potions-beat-prod-service
      ECS_BEAT_TASK: rozana-potions-beat-prod

      ECS_FLOWER_SERVICE: rozana-potions-flower-prod-service
      ECS_FLOWER_TASK: rozana-potions-flower-prod

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Ensure deploy script is executable
        run: chmod +x scripts/deploy-service.sh

      # ----------------- Deployments in order -----------------
      # ----------------- Deployments in order -----------------
      - name: Deploy potions-api
        run: ./scripts/deploy-service.sh "$ECS_CLUSTER" "$ECS_API_SERVICE" "$ECS_API_TASK" "$ECR_REGISTRY/$ECR_REPOSITORY:$ECR_IMAGE_TAG" "$ECR_IMAGE_TAG" "$ECR_REPOSITORY"

      - name: Deploy potions-celery
        run: ./scripts/deploy-service.sh "$ECS_CLUSTER" "$ECS_CELERY_SERVICE" "$ECS_CELERY_TASK" "$ECR_REGISTRY/$ECR_REPOSITORY:$ECR_IMAGE_TAG" "$ECR_IMAGE_TAG" "$ECR_REPOSITORY"

      - name: Deploy potions-beat
        run: ./scripts/deploy-service.sh "$ECS_CLUSTER" "$ECS_BEAT_SERVICE" "$ECS_BEAT_TASK" "$ECR_REGISTRY/$ECR_REPOSITORY:$ECR_IMAGE_TAG" "$ECR_IMAGE_TAG" "$ECR_REPOSITORY"

      - name: Deploy potions-flower
        run: ./scripts/deploy-service.sh "$ECS_CLUSTER" "$ECS_FLOWER_SERVICE" "$ECS_FLOWER_TASK" "$ECR_REGISTRY/$ECR_REPOSITORY:$ECR_IMAGE_TAG" "$ECR_IMAGE_TAG" "$ECR_REPOSITORY"
