services:
  db1:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_DB=potions
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=rozana^1234
    ports:
      - "5439:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  api:
    container_name: api_service
    build:
      context: .
      dockerfile: Dockerfile-prod
    volumes:
      - ./app/potions:/application
    ports:
      - "8004:8000"
    env_file:
      - .env
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health/ || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 5
    depends_on:
      db1:
        condition: service_healthy
      redis:
        condition: service_healthy
    stdin_open: true
    tty: true
    command: uvicorn potions.asgi:application --host 0.0.0.0 --port 8000 --reload

  celery_worker:
    container_name: celery_worker
    build:
      context: .
      dockerfile: Dockerfile-prod
    env_file:
      - .env
    depends_on:
      api:
        condition: service_healthy
    command: ['celery', '-A', 'potions.celery', 'worker', '-l', 'info']

  celery_beat:
    container_name: celery_beat
    build:
      context: .
      dockerfile: Dockerfile-prod
    env_file:
      - .env
    depends_on:
      api:
        condition: service_healthy
    command: ['celery', '-A', 'potions.celery', 'beat', '-l', 'info', '--scheduler', 'django_celery_beat.schedulers:DatabaseScheduler']

  flower:
    container_name: flower
    build:
      context: .
      dockerfile: Dockerfile-prod
    env_file:
      - .env
    depends_on:
      api:
        condition: service_healthy
    ports:
      - "5555:5555"
    # add password --basic-auth=your_username:your_password
    command: ['celery', '-A', 'potions.celery', 'flower', '--url-prefix=flower', '--basic-auth=admin:admin']

volumes:
  postgres_data: