from potions.logging.utils import get_app_logger
from django.http import JsonResponse
from django.conf import settings

logger = get_app_logger('webhook_middleware')


class WebhookAuthenticationMiddleware:
    """
    Middleware to handle webhook authentication for T<PERSON> and other webhook endpoints.

    This middleware validates static bearer tokens for webhook endpoints and provides
    consistent authentication across all webhook routes.
    """

    # Define which URL patterns require webhook authentication and their corresponding tokens
    TOKEN_MAPPING = {
        '/api/potions/integrations/webhooks/tms/': 'TMS_WEBHOOK_TOKEN',
        '/api/potions/integrations/invoice/callback/': 'WMS_WEBHOOK_TOKEN',
        '/api/potions/payments/webhooks/': 'RAZORPAY_WEBHOOK_SECRET',
        '/api/potions/integrations/webhooks/wms/': 'WMS_WEBHOOK_TOKEN'
    }

    # Extract webhook paths from token mapping
    WEBHOOK_PATHS = list(TOKEN_MAPPING.keys())

    def __init__(self, get_response):
        """
        Initialize the middleware.

        Args:
            get_response: The next middleware or view in the chain
        """
        self.get_response = get_response

    def __call__(self, request):
        """
        Process the request through the webhook authentication middleware.
        Args:
            request: The HTTP request object
        Returns:
            HTTP response from the next middleware/view or authentication error
        """
        # Check if this request path requires webhook authentication
        if self._requires_webhook_auth(request.path):
            # Validate webhook authentication
            auth_result = self._validate_webhook_token(request)

            if not auth_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': 'Webhook authentication failed',
                    'details': auth_result['error']
                }, status=401)

            # Log successful webhook authentication
            logger.info(f"Webhook authentication successful for path: {request.path}")

        # Continue to the next middleware or view
        response = self.get_response(request)
        return response

    def __init__(self, get_response):
        """
        Initialize the middleware.
        Args:
            get_response: The next middleware or view in the chain
        """
        self.get_response = get_response

    def __call__(self, request):
        """
        Process the request through the webhook authentication middleware.
        Args:
            request: The HTTP request object
        Returns:
            HTTP response from the next middleware/view or authentication error
        """
        # Check if this request path requires webhook authentication
        if self._requires_webhook_auth(request.path):
            # Validate webhook authentication
            auth_result = self._validate_webhook_token(request)

            if not auth_result['valid']:
                return JsonResponse({
                    'success': False,
                    'error': 'Webhook authentication failed',
                    'details': auth_result['error']
                }, status=401)

            # Log successful webhook authentication
            logger.info(f"Webhook authentication successful for path: {request.path}")

        # Continue to the next middleware or view
        response = self.get_response(request)
        return response

    def _requires_webhook_auth(self, path):
        """
        Check if the given path requires webhook authentication.
        Args:
            path: The request path to check
        Returns:
            bool: True if webhook authentication is required, False otherwise
        """
        return any(path.startswith(webhook_path) for webhook_path in self.WEBHOOK_PATHS)

    def _validate_webhook_token(self, request):
        """
        Validate webhook static bearer token from the Authorization header.
        Args:
            request: The HTTP request object
        Returns:
            dict: Authentication result with 'valid' and 'error' keys
        """
        # Determine which token to use based on the request path using TOKEN_MAPPING
        token_name = None
        webhook_token = None

        for path_pattern, token_setting in self.TOKEN_MAPPING.items():
            if request.path.startswith(path_pattern):
                token_name = token_setting
                webhook_token = getattr(settings, token_setting, None)
                break

        if not token_name:
            logger.error(f"No token mapping found for path: {request.path}")
            return {
                'valid': False,
                'error': 'No authentication configured for this webhook path'
            }

        if not webhook_token:
            logger.error(f"{token_name} not configured in settings")
            return {
                'valid': False,
                'error': 'Webhook authentication not configured'
            }

        # Check Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            logger.warning(f"Missing Authorization header in webhook request to {request.path}")
            return {
                'valid': False,
                'error': 'Authorization header missing'
            }

        # Validate Bearer token format
        if not auth_header.startswith('Bearer '):
            logger.warning(f"Invalid authorization header format in webhook request to {request.path}")
            return {
                'valid': False,
                'error': 'Invalid authorization header format. Expected: Bearer <token>'
            }

        # Extract and validate token
        try:
            token = auth_header.split(' ')[1]
            if token != webhook_token:
                logger.warning(f"Invalid webhook token attempted for {request.path}: {token[:10]}...")
                return {
                    'valid': False,
                    'error': 'Invalid webhook token'
                }
        except IndexError:
            logger.warning(f"Malformed Bearer token in webhook request to {request.path}")
            return {
                'valid': False,
                'error': 'Malformed Bearer token'
            }

        # Token is valid
        logger.debug(f"Valid webhook token for path: {request.path}")
        return {
            'valid': True,
            'error': None
        }

    def process_exception(self, request, exception):
        """
        Handle exceptions that occur during webhook request processing.
        Args:
            request: The HTTP request object
            exception: The exception that occurred
        Returns:
            None to let Django handle the exception normally
        """
        # Log webhook-related exceptions
        if hasattr(request, 'path') and self._requires_webhook_auth(request.path):
            logger.error(f"Exception in webhook middleware for path {request.path}: {str(exception)}")

        # Return None to let Django handle the exception normally
        return None
