from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, Dict, Any
from payments.constants import RefundStatus, PaymentStatus
from payments.services.razorpay_service import RazorpayService

# Repositories
from core.repository.payments import PaymentRepository
from core.repository.refunds_details import RefundRepository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_service')


class RefundService:
    def __init__(self):
        self.razorpay_service = RazorpayService()

    def create_refund(self, payment_id: str) -> Dict[str, Any]:
        try:
            payment_record = self._get_payment(payment_id)
            if not payment_record:
                logger.warning(f"Refund create: payment not found | payment_id={payment_id}")
                return {"success": False, "message": f"Payment {payment_id} not found"}
            
            if payment_record['payment_status'] != PaymentStatus.COMPLETED:
                logger.warning(f"Refund create: payment not completed | payment_id={payment_id} | status={payment_record['payment_status']}")
                return {"success": False, "message": f"Payment {payment_id} is not completed, cannot refund"}
            
            razorpay_result = self.razorpay_service.create_refund(payment_id, {"reason": "Full refund via POTIONS"})
            if not razorpay_result["success"]:
                return {"success": False, "message": razorpay_result.get("message", "Failed to create refund")}
            
            refund_id = razorpay_result["refund"]["id"]
            refund_amount = Decimal(str(payment_record['payment_amount']))
            
            self._create_refund_record(payment_record['id'], refund_id, refund_amount)
            
            razorpay_status = razorpay_result["refund"].get('status', '').lower()
            if razorpay_status == 'processed':
                self._update_refund_status(refund_id, RefundStatus.PROCESSED)
                self._update_payment_status(payment_id, PaymentStatus.REFUNDED)
            
            return {
                "success": True,
                "refund_id": refund_id,
                "refund_amount": float(refund_amount),
                "refund_status": RefundStatus.get_description(RefundStatus.PROCESSED if razorpay_status == 'processed' else RefundStatus.CREATED),
                "payment_id": payment_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "message": "Refund created successfully"
            }
            
        except Exception as e:
            logger.error(f"Refund creation error | payment_id={payment_id} error={e}", exc_info=True)
            return {"success": False, "message": f"Error creating refund: {str(e)}"}

    def create_refund_by_order_id(self, order_id: str) -> Dict[str, Any]:
        try:
            payment_details = PaymentRepository().get_payment_details_by_order_id_and_payment_mode(order_id, "razorpay")
            logger.info(f"Payment details for order {order_id}: {payment_details}")
            if not payment_details:
                logger.warning(f"Refund by order: no Razorpay payment found | order_id={order_id}")
                return {"success": False, "message": f"No Razorpay payment found for order {order_id}"}

            if payment_details.get('payment_status') != PaymentStatus.COMPLETED:
                logger.warning(f"Refund by order: payment not completed | order_id={order_id} | status={payment_details.get('payment_status')}")
                return {"success": False, "message": f"Payment {payment_details.get('payment_id')} is not completed, cannot refund"}

            # Trigger refund in Razorpay
            razorpay_response = self.razorpay_service.create_refund(payment_details.get("payment_id"), {"order_reference": order_id})
            logger.info(f"Refund response for order {order_id}: {razorpay_response}")
            if not razorpay_response.get("success"):
                logger.error(f"Failed to create refund for order {order_id}: {razorpay_response.get('error', 'Unknown error')}")
                return {"success": False, "message": razorpay_response.get("message", "Failed to create refund"), "error": razorpay_response.get("error", "Unknown error")}

            refund = razorpay_response.get("refund", {})
            refund_id = refund.get("id")
            razorpay_status = (refund.get('status') or '').lower()

            # Create refund record in OMS via repository
            refund_amount = Decimal(str(payment_details.get('payment_amount')))
            RefundRepository().create_refund_record(payment_details.get('id'), refund_id, refund_amount)
            logger.info(f"Refunded amount to customer: {refund_amount} for order {order_id} with refund id {refund_id}")

            # Update statuses if processed
            if razorpay_status == 'processed':
                RefundRepository().update_refund_status(refund_id, RefundStatus.PROCESSED)
                PaymentRepository().update_payment_details_status(payment_details.get('payment_id'), PaymentStatus.REFUNDED)

            return {
                "success": True,
                "refund_id": refund_id,
                "refund_amount": float(refund_amount),
                "refund_status": RefundStatus.get_description(RefundStatus.PROCESSED if razorpay_status == 'processed' else RefundStatus.CREATED),
                "payment_id": payment_details.get('payment_id'),
                "order_id": order_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "message": "Refund created successfully"
            }
        except Exception as e:
            logger.error(f"Refund creation by order_id error | order_id={order_id} error={e}", exc_info=True)
            return {"success": False, "message": f"Error creating refund for order {order_id}: {str(e)}"}
    
    def _get_payment(self, payment_id: str) -> Optional[Dict[str, Any]]:
        # Delegate to repository
        return PaymentRepository().get_payment_details_by_payment_id(payment_id)
    
    def _create_refund_record(self, payment_id: int, refund_id: str, refund_amount: Decimal):
        # Delegate to repository
        RefundRepository().create_refund_record(payment_id, refund_id, refund_amount)
    
    def _update_refund_status(self, refund_id: str, status: int):
        # Delegate to repository
        RefundRepository().update_refund_status(refund_id, status)
    
    def _update_payment_status(self, payment_id: str, status: int):
        # Delegate to repository
        PaymentRepository().update_payment_details_status(payment_id, status)
    
    def get_refund_by_id(self, refund_id: str) -> Optional[Dict]:
        try:
            return RefundRepository().get_refund_by_id(refund_id)
        except Exception as e:
            logger.error(f"Refund fetch error | refund_id={refund_id} error={e}", exc_info=True)
            return None
    
    def process_refund_webhook(self, refund_id: str, webhook_status: str, webhook_data: Dict[str, Any]) -> Dict[str, Any]:
        try:
            status_mapping = {
                "created": RefundStatus.CREATED,
                "pending": RefundStatus.PENDING,
                "processed": RefundStatus.PROCESSED,
                "failed": RefundStatus.FAILED,
                "cancelled": RefundStatus.FAILED
            }

            new_status = status_mapping.get(webhook_status.lower(), RefundStatus.PENDING)
            self._update_refund_status(refund_id, new_status)
            
            return {"success": True, "new_status": RefundStatus.get_description(new_status)}
            
        except Exception as e:
            logger.error(f"Webhook processing error | refund_id={refund_id} error={e}", exc_info=True)
            return {"success": False, "message": f"Error processing webhook: {str(e)}"}


refund_service = RefundService()
