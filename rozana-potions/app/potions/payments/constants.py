"""
Constants for payment operations - copied from OMS to maintain consistency
"""

class PaymentStatus:
    """Payment status constants - matching OMS exactly"""
    PENDING = 50
    COMPLETED = 51
    FAILED = 52
    REFUNDED = 53
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of payment status"""
        descriptions = {
            cls.PENDING: "Pending",
            cls.COMPLETED: "Completed", 
            cls.FAILED: "Failed",
            cls.REFUNDED: "Refunded"
        }
        return descriptions.get(status_code, "Unknown")
    
    @classmethod
    def is_final_status(cls, status_code: int) -> bool:
        """Check if status is final (no further changes expected)"""
        return status_code in [cls.COMPLETED, cls.FAILED, cls.REFUNDED]


class RefundStatus:
    """Refund status constants"""
    CREATED = 60
    PENDING = 61
    PROCESSED = 62
    FAILED = 63
    
    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of refund status"""
        descriptions = {
            cls.CREATED: "Created",
            cls.PENDING: "Pending",
            cls.PROCESSED: "Processed",
            cls.FAILED: "Failed"
        }
        return descriptions.get(status_code, "Unknown")


class OrderStatus:
    """Order status constants - matching OMS exactly"""
    PENDING = 10
    OPEN = 11
    CONFIRMED = 12
    CANCELLED = 13
    COMPLETED = 14

    WMS_CANCELED = 28

    @classmethod
    def get_description(cls, status_code: int) -> str:
        """Get human-readable description of order status"""
        descriptions = {
            cls.PENDING: "Pending",
            cls.OPEN: "Open",
            cls.CONFIRMED: "Confirmed",
            cls.CANCELLED: "Cancelled",
            cls.COMPLETED: "Completed",
            cls.WMS_CANCELED: "WMS Cancelled"
        }
        return descriptions.get(status_code, "Unknown")
