import json
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from payments.serializers import RefundCreateSerializer
from payments.refund_service import RefundService
from payments.services.razorpay_service import RazorpayService
from payments.constants import RefundStatus
from potions.logging.utils import get_app_logger

logger = get_app_logger('payments_views')


class CreateRefundView(APIView):
    def post(self, request):
        serializer = RefundCreateSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Refund create: invalid request data | errors={serializer.errors}")
            return Response({
                "success": False, 
                "message": "Invalid request data", 
                "errors": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            refund_result = RefundService().create_refund(
                serializer.validated_data['payment_id']
            )
            
            if not refund_result["success"]:
                return Response({
                    "success": False,
                    "message": refund_result.get("message", "Failed to create refund")
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
            return Response(refund_result, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Refund creation error: {e}", exc_info=True)
            return Response({
                "success": False,
                "message": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetRefundStatusView(APIView):
    def get(self, request, refund_id):
        try:
            refund_record = RefundService().get_refund_by_id(refund_id)
            
            if not refund_record:
                return Response({
                    "success": False,
                    "message": f"Refund {refund_id} not found"
                }, status=status.HTTP_404_NOT_FOUND)
            
            return Response({
                "success": True,
                "refund_id": refund_record['refund_id'],
                "payment_id": refund_record['payment_id'],
                "refund_amount": float(refund_record['refund_amount']),
                "refund_status": RefundStatus.get_description(refund_record['refund_status']),
                "refund_date": refund_record['refund_date'].isoformat() if refund_record['refund_date'] else None,
                "created_at": refund_record['created_at'].isoformat(),
                "updated_at": refund_record['updated_at'].isoformat()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Refund status error | refund_id={refund_id} error={e}", exc_info=True)
            return Response({
                "success": False,
                "message": "Internal server error"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class RazorpayWebhookView(APIView):
    def post(self, request):
        raw_body = request.body
        signature = request.META.get('HTTP_X_RAZORPAY_SIGNATURE')

        if not signature:
            logger.warning("Razorpay webhook: missing X-Razorpay-Signature header")
            return Response({"error": "Missing X-Razorpay-Signature header"}, 
                          status=status.HTTP_400_BAD_REQUEST)

        try:
            razorpay_service = RazorpayService()
            is_verified = razorpay_service.verify_webhook_signature(raw_body, signature)
            if not is_verified:
                logger.warning("Razorpay webhook: invalid signature")
                return Response({"error": "Invalid signature"}, status=status.HTTP_400_BAD_REQUEST)

            webhook_data = json.loads(raw_body.decode('utf-8'))
            event = webhook_data.get("event")
            logger.info(f"Razorpay webhook received | event={event}")

            if event in ["refund.created", "refund.processed", "refund.failed"]:
                refund_entity = webhook_data.get("payload", {}).get("refund", {}).get("entity", {})
                refund_id = refund_entity.get("id")
                refund_status = refund_entity.get("status")
                
                if refund_id and refund_status:
                    refund_processor.process_refund_webhook(refund_id, refund_status, refund_entity)

            return Response({"status": "ok", "event": event}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Webhook error: {e}", exc_info=True)
            return Response({"error": "Internal server error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
