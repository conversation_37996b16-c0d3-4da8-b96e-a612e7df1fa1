from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, Dict, Any
from pydantic import BaseModel
from payments.constants import RefundStatus, PaymentStatus
from payments.services.razorpay_service import RazorpayService
from payments.services.wallet_service import WalletService

# Repositories
from core.repository.payments import PaymentRepository
from core.repository.refunds_details import RefundRepository
from core.repository.orders import OrderRepository
from core.repository.sales_return import sales_return_repository

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_processor')

class RefundProcessorReturnMessage(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None

class RefundProcessor:
    def __init__(self):
        self.razorpay_service = RazorpayService()
        self.wallet_service = WalletService()
        self.payment_repository = PaymentRepository()
        self.refund_repository = RefundRepository()
        self.order_repository = OrderRepository()
        # Sales return repository singleton
        self.sales_return_repository = sales_return_repository

    def create_refund_for_cancelled_quantity(self, order_id: str) -> RefundProcessorReturnMessage:
        """Create refunds based on cancelled quantity for order items"""
        try:
            logger.info(f"Processing cancelled quantity refunds for order {order_id}")
            
            # Get order items with cancelled quantity > refunded quantity
            items_to_refund = self.order_repository.get_order_items_with_cancelled_quantity(order_id)
            
            if not items_to_refund:
                return RefundProcessorReturnMessage(
                    success=True, 
                    message=f"No items with pending cancelled quantity refunds for order {order_id}"
                )
            
            logger.info(f"Found {len(items_to_refund)} items requiring refunds")
            
            # Calculate total refund amount needed
            total_refund_needed = Decimal('0')
            item_refunds = []
            
            for item in items_to_refund:
                cancelled_qty = Decimal(str(item.get('cancelled_quantity', 0)))
                refunded_qty = Decimal(str(item.get('refunded_quantity', 0)))
                total_qty = Decimal(str(item.get('quantity', 0)))
                sale_price = Decimal(str(item.get('sale_price', 0)))
                
                # Calculate pending refund quantity and amount
                pending_refund_qty = cancelled_qty - refunded_qty
                if pending_refund_qty > 0:
                    refund_amount = pending_refund_qty * sale_price
                    total_refund_needed += refund_amount

                    item_refunds.append({
                        'item_id': item['id'],
                        'sku': item.get('sku', ''),
                        'pending_refund_qty': float(pending_refund_qty),
                        'refund_amount': float(refund_amount),
                        'new_refunded_qty': float(cancelled_qty)
                    })

            if total_refund_needed <= 0:
                return RefundProcessorReturnMessage(
                    success=True, 
                    message=f"No refund amount needed for order {order_id}"
                )

            logger.info(f"Total refund needed: {total_refund_needed}")

            # Get customer ID
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                logger.warning(f"No customer found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")

            # Get completed payments with priority (Razorpay first, then wallet)
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            completed_payments = []
            for payment in all_payments:
                if payment.get('payment_status') == PaymentStatus.COMPLETED:
                    completed_payments.append(payment)

            if not completed_payments:
                logger.warning(f"No completed payments found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No completed payments found for order {order_id}")

            # Get already refunded amounts per payment_id
            refunded_amounts = self.refund_repository.get_refunded_amounts_by_order_id(order_id)
            logger.info(f"Already refunded amounts: {refunded_amounts}")

            # Calculate remaining refundable amount for each payment
            available_payments = []
            for payment in completed_payments:
                payment_id = payment.get('id')
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                already_refunded = refunded_amounts.get(payment_id, Decimal('0'))
                remaining_amount = payment_amount - already_refunded
                
                if remaining_amount > 0:
                    payment['remaining_refundable'] = remaining_amount
                    available_payments.append(payment)
                    logger.info(f"Payment {payment_id}: Original={payment_amount}, Refunded={already_refunded}, Remaining={remaining_amount}")
            
            if not available_payments:
                logger.warning(f"No refundable amount remaining for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No refundable amount remaining for order {order_id}")
            
            # Sort payments by priority: Razorpay first, then wallet/cash
            available_payments.sort(key=lambda p: (0 if p.get('payment_mode', '').lower() == 'razorpay' else 1))

            # Process refunds with priority and remaining amounts
            remaining_refund = total_refund_needed
            refund_results = []
            
            for payment in available_payments:
                if remaining_refund <= 0:
                    break
                    
                remaining_refundable = payment['remaining_refundable']
                refund_amount = min(remaining_refund, remaining_refundable)
                
                if refund_amount > 0:
                    logger.info(f"Processing refund of {refund_amount} from payment {payment.get('id')} (Remaining refundable: {remaining_refundable})")
                    
                    payment_mode = payment.get('payment_mode', '').lower()
                    
                    if payment_mode == 'razorpay':
                        refund_result = self._process_razorpay_refund(payment, order_id, float(refund_amount))
                    elif payment_mode in ['cash', 'wallet', 'cod']:
                        refund_result = self._process_wallet_refund(payment, order_id, customer_id, float(refund_amount))
                    else:
                        logger.warning(f"Unsupported payment mode: {payment_mode}")
                        continue
                    
                    if refund_result.success:
                        remaining_refund -= refund_amount
                        refund_results.append(refund_result.data)
                        logger.info(f"Successfully processed refund of {refund_amount}, remaining to refund: {remaining_refund}")
                    else:
                        logger.error(f"Refund failed: {refund_result.message}")
            
            # Update refunded quantities for all items
            if remaining_refund <= 0:  # All refunds successful
                for item_refund in item_refunds:
                    updated = self.order_repository.update_order_item_refunded_quantity(
                        item_id=item_refund['item_id'],
                        refunded_quantity=item_refund['new_refunded_qty']
                    )
                    if not updated:
                        logger.error(f"Failed to update refunded quantity for item {item_refund['item_id']}")
            
            success = remaining_refund <= 0
            final_result = {
                "success": success,
                "order_id": order_id,
                "total_refund_needed": float(total_refund_needed),
                "total_refund_processed": float(total_refund_needed - remaining_refund),
                "remaining_refund": float(remaining_refund),
                "item_refunds": item_refunds,
                "refund_results": refund_results,
                "message": "Cancelled quantity refunds processed successfully" if success else "Partial refund processing completed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            return RefundProcessorReturnMessage(success=success, message=final_result["message"], data=final_result)
            
        except Exception as e:
            logger.error(f"Cancelled quantity refund error | order_id={order_id} error={e}", exc_info=True)
            return RefundProcessorReturnMessage(success=False, message=f"Error processing cancelled quantity refunds: {str(e)}")

    def create_refund_for_unfulfilled_quantity(self, order_id: str) -> RefundProcessorReturnMessage:
        """
        Create refunds based on unfulfilled quantity for order items.
        Refund formula: (unfulfilled_quantity - cancelled_quantity) 
        Since WMS reports cancelled items as unfulfilled which we've already refunded.
        """
        try:
            logger.info(f"[UNFULFILLED_REFUND] Processing unfulfilled quantity refunds for order {order_id}")
            
            # Get order items with unfulfilled quantity requiring refund
            items_to_refund = self.order_repository.get_order_items_with_unfulfilled_quantity(order_id)
            
            if not items_to_refund:
                return RefundProcessorReturnMessage(
                    success=True, 
                    message=f"No items with pending unfulfilled quantity refunds for order {order_id}"
                )
            
            logger.info(f"[UNFULFILLED_REFUND] Found {len(items_to_refund)} items requiring unfulfilled refunds")
            
            # Calculate total refund amount needed
            total_refund_needed = Decimal('0')
            item_refunds = []
            
            for item in items_to_refund:
                unfulfilled_qty = Decimal(str(item.get('unfulfilled_quantity', 0)))
                cancelled_qty = Decimal(str(item.get('cancelled_quantity', 0)))
                refunded_qty = Decimal(str(item.get('refunded_quantity', 0)))
                sale_price = Decimal(str(item.get('sale_price', 0)))
                
                # Calculate pending unfulfilled refund quantity
                # Logic: unfulfilled_qty - cancelled_qty - (refunded_qty - cancelled_qty)
                # This ensures we don't double refund cancelled items or already refunded unfulfilled items
                max_refundable_for_unfulfilled = unfulfilled_qty - cancelled_qty
                already_refunded_for_unfulfilled = max(Decimal('0'), refunded_qty - cancelled_qty)
                pending_unfulfilled_refund_qty = max_refundable_for_unfulfilled - already_refunded_for_unfulfilled
                
                if pending_unfulfilled_refund_qty > 0:
                    refund_amount = pending_unfulfilled_refund_qty * sale_price
                    total_refund_needed += refund_amount
                    
                    # New refunded quantity will be current + pending unfulfilled
                    new_refunded_qty = refunded_qty + pending_unfulfilled_refund_qty

                    item_refunds.append({
                        'item_id': item['id'],
                        'sku': item.get('sku', ''),
                        'wh_sku': item.get('wh_sku', ''),
                        'unfulfilled_qty': float(unfulfilled_qty),
                        'cancelled_qty': float(cancelled_qty),
                        'current_refunded_qty': float(refunded_qty),
                        'max_refundable_for_unfulfilled': float(max_refundable_for_unfulfilled),
                        'already_refunded_for_unfulfilled': float(already_refunded_for_unfulfilled),
                        'pending_unfulfilled_refund_qty': float(pending_unfulfilled_refund_qty),
                        'refund_amount': float(refund_amount),
                        'new_refunded_qty': float(new_refunded_qty)
                    })

            if total_refund_needed <= 0:
                return RefundProcessorReturnMessage(
                    success=True, 
                    message=f"No unfulfilled refund amount needed for order {order_id}"
                )

            logger.info(f"[UNFULFILLED_REFUND] Total unfulfilled refund needed: {total_refund_needed}")

            # Get customer ID
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                logger.warning(f"[UNFULFILLED_REFUND] No customer found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")

            # Get completed payments with priority (Razorpay first, then wallet)
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            completed_payments = []
            for payment in all_payments:
                if payment.get('payment_status') == PaymentStatus.COMPLETED:
                    completed_payments.append(payment)

            if not completed_payments:
                logger.warning(f"[UNFULFILLED_REFUND] No completed payments found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No completed payments found for order {order_id}")

            # Get already refunded amounts per payment_id
            refunded_amounts = self.refund_repository.get_refunded_amounts_by_order_id(order_id)
            logger.info(f"[UNFULFILLED_REFUND] Already refunded amounts: {refunded_amounts}")

            # Calculate remaining refundable amount for each payment
            available_payments = []
            for payment in completed_payments:
                payment_id = payment.get('id')
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                already_refunded = refunded_amounts.get(payment_id, Decimal('0'))
                remaining_amount = payment_amount - already_refunded
                
                if remaining_amount > 0:
                    payment['remaining_refundable'] = remaining_amount
                    available_payments.append(payment)
                    logger.info(f"[UNFULFILLED_REFUND] Payment {payment_id}: Original={payment_amount}, Refunded={already_refunded}, Remaining={remaining_amount}")
            
            if not available_payments:
                logger.warning(f"[UNFULFILLED_REFUND] No refundable amount remaining for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No refundable amount remaining for order {order_id}")
            
            # Sort payments by priority: Razorpay first, then wallet/cash
            available_payments.sort(key=lambda p: (0 if p.get('payment_mode', '').lower() == 'razorpay' else 1))

            # Process refunds with priority and remaining amounts
            remaining_refund = total_refund_needed
            refund_results = []
            
            for payment in available_payments:
                if remaining_refund <= 0:
                    break
                    
                remaining_refundable = payment['remaining_refundable']
                refund_amount = min(remaining_refund, remaining_refundable)
                
                if refund_amount > 0:
                    logger.info(f"[UNFULFILLED_REFUND] Processing refund of {refund_amount} from payment {payment.get('id')} (Remaining refundable: {remaining_refundable})")
                    
                    payment_mode = payment.get('payment_mode', '').lower()
                    
                    if payment_mode == 'razorpay':
                        refund_result = self._process_razorpay_refund(payment, order_id, float(refund_amount))
                    elif payment_mode in ['cash', 'wallet', 'cod']:
                        refund_result = self._process_wallet_refund(payment, order_id, customer_id, float(refund_amount))
                    else:
                        logger.warning(f"[UNFULFILLED_REFUND] Unsupported payment mode: {payment_mode}")
                        continue
                    
                    if refund_result.success:
                        remaining_refund -= refund_amount
                        refund_results.append(refund_result.data)
                        logger.info(f"[UNFULFILLED_REFUND] Successfully processed refund of {refund_amount}, remaining to refund: {remaining_refund}")
                    else:
                        logger.error(f"[UNFULFILLED_REFUND] Refund failed: {refund_result.message}")
            
            # Update refunded quantities for all items if all refunds successful
            success = remaining_refund <= 0
            if success:
                # Calculate the success ratio for proportional updates
                refund_success_ratio = float((total_refund_needed - remaining_refund) / total_refund_needed)
                
                for item_refund in item_refunds:
                    # Calculate proportional refunded quantity based on success ratio
                    proportional_refund_qty = item_refund['pending_unfulfilled_refund_qty'] * refund_success_ratio
                    final_refunded_qty = item_refund['current_refunded_qty'] + proportional_refund_qty
                    
                    updated = self.order_repository.update_order_item_refunded_quantity(
                        item_id=item_refund['item_id'],
                        refunded_quantity=final_refunded_qty
                    )
                    if updated:
                        logger.info(f"[UNFULFILLED_REFUND] Updated refunded quantity for item {item_refund['item_id']} to {final_refunded_qty}")
                    else:
                        logger.error(f"[UNFULFILLED_REFUND] Failed to update refunded quantity for item {item_refund['item_id']}")
            
            final_result = {
                "success": success,
                "order_id": order_id,
                "total_refund_needed": float(total_refund_needed),
                "total_refund_processed": float(total_refund_needed - remaining_refund),
                "remaining_refund": float(remaining_refund),
                "item_refunds": item_refunds,
                "refund_results": refund_results,
                "message": "Unfulfilled quantity refunds processed successfully" if success else "Partial unfulfilled refund processing completed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            return RefundProcessorReturnMessage(success=success, message=final_result["message"], data=final_result)
            
        except Exception as e:
            logger.error(f"[UNFULFILLED_REFUND] Unfulfilled quantity refund error | order_id={order_id} error={e}", exc_info=True)
            return RefundProcessorReturnMessage(success=False, message=f"Error processing unfulfilled quantity refunds: {str(e)}")

    def _is_pos_order(self, order_id: str) -> bool:
        """
        Check if an order is a POS order by examining the order_mode field.
        
        Args:
            order_id: The order ID to check
            
        Returns:
            True if the order is a POS order, False otherwise
        """
        try:
            order_data = self.order_repository.get_order_by_id(order_id)
            if order_data:
                order_mode = order_data.get('order_mode', '').lower()
                return order_mode == 'pos'
            return False
        except Exception as e:
            logger.error(f"[GRN_REFUND] Error checking if order {order_id} is POS: {e}", exc_info=True)
            return False

    def create_refund_from_grn_callback(self, order_id: str, refund_amount: float, return_reference: str) -> RefundProcessorReturnMessage:
        """
        Create refunds directly from GRN callback with calculated amount.
        For POS orders: ALWAYS refund to wallet regardless of original payment method.
        For regular orders: Use existing priority logic (Razorpay first, then wallet).
        
        Args:
            order_id: The order ID to process refunds for
            refund_amount: Pre-calculated refund amount from GRN payload
            return_reference: Return reference for tracking
        """
        try:
            logger.info(f"[GRN_REFUND] Processing refund for order: {order_id}, amount: {refund_amount}, return_ref: {return_reference}")
            
            if refund_amount <= 0:
                logger.info(f"[GRN_REFUND] No refund amount needed for order: {order_id}")
                return RefundProcessorReturnMessage(
                    success=True,
                    message="No refund amount needed",
                    data={"order_id": order_id, "refund_amount": 0}
                )
            
            total_refund_needed = Decimal(str(refund_amount))
            
            # Check if this is a POS order
            is_pos_order = self._is_pos_order(order_id)
            logger.info(f"[GRN_REFUND] Order {order_id} is POS order: {is_pos_order}")
            
            # Get customer ID
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                logger.error(f"[GRN_REFUND] No customer found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")
            
            # Get all payments for the order
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            
            if not all_payments:
                logger.error(f"[GRN_REFUND] No payments found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No payments found for order {order_id}")
            
            # Filter payments and calculate remaining refundable amounts
            available_payments = []
            for payment in all_payments:
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                
                # Get existing refunds for this payment
                existing_refunds = self.refund_repository.get_refunds_for_payment(payment.get('id'))
                total_refunded = sum(Decimal(str(refund.get('refund_amount', 0))) for refund in existing_refunds)
                remaining_amount = payment_amount - total_refunded
                
                # Skip payments with no remaining amount
                if remaining_amount <= 0:
                    continue
                    
                payment['remaining_refundable'] = remaining_amount
                available_payments.append(payment)
                logger.info(f"[GRN_REFUND] Payment {payment.get('id')}: Original={payment_amount}, Refunded={total_refunded}, Remaining={remaining_amount}")
            
            if not available_payments:
                logger.error(f"[GRN_REFUND] No refundable payments found for order {order_id}")
                return RefundProcessorReturnMessage(success=False, message=f"No refundable payments found for order {order_id}")
            
            # For POS orders: Process ALL refunds to wallet regardless of payment method
            # For regular orders: Use existing priority logic (Razorpay first, then wallet)
            if is_pos_order:
                logger.info(f"[GRN_REFUND] POS order detected - processing ALL refunds to wallet")
                # For POS orders, sort by payment date (oldest first) for consistent processing
                available_payments.sort(key=lambda p: p.get('payment_date', ''))
            else:
                # Regular order logic: Sort by Razorpay first, then by created date
                available_payments.sort(key=lambda p: (0 if p.get('payment_mode', '').lower() == 'razorpay' else 1, p.get('payment_date')))

            # Process refunds with priority and remaining amounts
            remaining_refund = total_refund_needed
            refund_results = []
            
            for payment in available_payments:
                if remaining_refund <= 0:
                    break
                    
                remaining_refundable = payment['remaining_refundable']
                refund_amount_to_process = min(remaining_refund, remaining_refundable)
                
                if refund_amount_to_process > 0:
                    logger.info(f"[GRN_REFUND] Processing refund of {refund_amount_to_process} from payment {payment.get('id')}")
                    
                    payment_mode = payment.get('payment_mode', '').lower()
                    
                    # For POS orders: ALWAYS use wallet refund regardless of original payment method
                    if is_pos_order:
                        logger.info(f"[GRN_REFUND] POS order - forcing wallet refund for {payment_mode} payment")
                        refund_result = self._process_wallet_refund(payment, order_id, customer_id, float(refund_amount_to_process))
                    else:
                        # Regular order logic: Use payment method priority
                        if payment_mode == 'razorpay':
                            refund_result = self._process_razorpay_refund(payment, order_id, float(refund_amount_to_process))
                        elif payment_mode in ['cash', 'wallet', 'cod']:
                            refund_result = self._process_wallet_refund(payment, order_id, customer_id, float(refund_amount_to_process))
                        else:
                            logger.warning(f"[GRN_REFUND] Unsupported payment mode: {payment_mode}")
                            continue
                    
                    if refund_result.success:
                        remaining_refund -= refund_amount_to_process
                        refund_results.append(refund_result.data)
                        
                        logger.info(f"[GRN_REFUND] Successfully processed refund of {refund_amount_to_process}, remaining: {remaining_refund}")
                    else:
                        logger.error(f"[GRN_REFUND] Refund failed: {refund_result.message}")
            
            # Post-processing: Update statuses after successful refunds
            success = remaining_refund <= 0
            if success or refund_results:  # If any refunds were processed
                try:
                    # 1. Update refunded_quantity in order_items table
                    self._update_order_items_refunded_quantity(order_id, return_reference)
                    
                    # 2. Update refund_status in returns table
                    self._update_returns_refund_status(return_reference)
                    
                    # 3. Check if complete refund and update payment status if needed
                    if success:  # Complete refund successful
                        self._update_payment_status_if_fully_refunded(order_id)
                        
                except Exception as e:
                    logger.error(f"[GRN_REFUND] Error updating statuses: {e}", exc_info=True)
            
            # Prepare final result
            final_result = {
                "success": success,
                "order_id": order_id,
                "return_reference": return_reference,
                "total_refund_needed": float(total_refund_needed),
                "total_refund_processed": float(total_refund_needed - remaining_refund),
                "remaining_refund": float(remaining_refund),
                "refund_results": refund_results,
                "message": "GRN refund processed successfully" if success else "Partial GRN refund processing completed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            return RefundProcessorReturnMessage(success=success, message=final_result["message"], data=final_result)
        except Exception as e:
            logger.error(f"[GRN_REFUND] Error processing refund for order {order_id}: {str(e)}", exc_info=True)
            return RefundProcessorReturnMessage(
                success=False,
                message=f"Failed to process GRN refund: {str(e)}",
                data={"order_id": order_id, "return_reference": return_reference}
            )

    def _update_order_items_refunded_quantity(self, order_id: str, return_reference: str):
        """Update refunded_quantity in order_items table based on return items"""
        try:
            # Get return items for this return_reference
            return_items = self.sales_return_repository.get_return_items_by_reference(return_reference) or []
            
            for item in return_items:
                # Support both GRN payload-style keys and DB-style keys
                sku_code = item.get('sku_code') or item.get('sku')
                returned_qty = item.get('sales_return_quantity', 0) or item.get('quantity_returned', 0)
                
                if sku_code and returned_qty > 0:
                    # Update refunded_quantity in order_items
                    updated = self.order_repository.increment_order_item_refunded_quantity(
                        order_id=order_id,
                        sku_code=sku_code,
                        increment_qty=returned_qty
                    )
                    if updated:
                        logger.info(f"[GRN_REFUND] Updated refunded_quantity for {sku_code}: +{returned_qty}")
                    else:
                        logger.warning(f"[GRN_REFUND] Failed to update refunded_quantity for {sku_code}")
                        
        except Exception as e:
            logger.error(f"[GRN_REFUND] Error updating order items refunded quantity: {e}", exc_info=True)
    
    def _update_returns_refund_status(self, return_reference: str):
        """Update refund_status in returns table to REFUNDED"""
        try:
            updated = self.sales_return_repository.update_return_refund_status(
                return_reference=return_reference,
                refund_status='REFUNDED'
            )
            if updated:
                logger.info(f"[GRN_REFUND] Updated return refund_status to REFUNDED for {return_reference}")
            else:
                logger.warning(f"[GRN_REFUND] Failed to update return refund_status for {return_reference}")
                
        except Exception as e:
            logger.error(f"[GRN_REFUND] Error updating return refund status: {e}", exc_info=True)
    
    def _update_payment_status_if_fully_refunded(self, order_id: str):
        """Update payment_status to 53 (REFUNDED) if all payments are fully refunded"""
        try:
            # Get all payments for the order
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            
            for payment in all_payments:
                payment_id = payment.get('id')
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                
                # Get total refunded for this payment
                existing_refunds = self.refund_repository.get_refunds_for_payment(payment_id)
                total_refunded = sum(Decimal(str(refund.get('refund_amount', 0))) for refund in existing_refunds)
                
                # If fully refunded, update payment status to 53 (REFUNDED)
                if total_refunded >= payment_amount:
                    updated = self.payment_repository.update_payment_details_status(
                        payment.get('payment_id'), 
                        53  # PaymentStatus.REFUNDED
                    )
                    if updated:
                        logger.info(f"[GRN_REFUND] Updated payment status to REFUNDED for payment {payment.get('payment_id')}")
                    else:
                        logger.warning(f"[GRN_REFUND] Failed to update payment status for {payment.get('payment_id')}")
                        
        except Exception as e:
            logger.error(f"[GRN_REFUND] Error updating payment status: {e}", exc_info=True)


    def create_refund_for_returned_quantity(self, order_id: str) -> RefundProcessorReturnMessage:
        """
        Create refunds for returned quantities based on return status.
        Only processes returns with approved status and pending refund_status.
        """
        try:
            logger.info(f"[REFUND_PROCESSOR] Starting return quantity refund processing for order: {order_id}")
            
            # Get return items that need refunding (approved status with pending refund)
            return_items = self.order_repository.get_return_items_for_refund(str(order_id))
            
            if not return_items:
                logger.info(f"[REFUND_PROCESSOR] No pending return items found for refund for order: {order_id}")
                return RefundProcessorReturnMessage(
                    success=True,
                    message="No pending return items found for refund",
                    refund_details=[]
                )
            
            logger.info(f"[REFUND_PROCESSOR] Found {len(return_items)} return items for refund")
            
            # Calculate total refund amount needed
            total_refund_needed = Decimal('0')
            returns_to_process = {}  # Group by return_id
            
            for item in return_items:
                return_id = item['return_id']
                if return_id not in returns_to_process:
                    returns_to_process[return_id] = {
                        'return_id': return_id,
                        'return_reference': item['return_reference'],
                        'items': [],
                        'total_amount': Decimal('0')
                    }
                
                # Calculate refund amount for this item
                item_refund_amount = Decimal(str(item['sale_price'])) * Decimal(str(item['quantity_returned']))
                total_refund_needed += item_refund_amount
                
                returns_to_process[return_id]['items'].append({
                    'return_item_id': item['return_item_id'],
                    'order_item_id': item['order_item_id'],
                    'sku': item['sku'],
                    'quantity_returned': item['quantity_returned'],
                    'unit_price': Decimal(str(item['sale_price'])),
                    'refund_amount': item_refund_amount
                })
                returns_to_process[return_id]['total_amount'] += item_refund_amount
            
            if total_refund_needed <= 0:
                logger.info(f"[REFUND_PROCESSOR] No refund amount needed for returned quantities in order: {order_id}")
                return RefundProcessorReturnMessage(
                    success=True,
                    message="No refund amount needed for returned quantities",
                    refund_details=[]
                )
                
            logger.info(f"[REFUND_PROCESSOR] Total refund needed for returned quantities: {total_refund_needed}")
            
            # Get customer ID
            customer_id = self.order_repository.get_customer_id_by_order_id(order_id)
            if not customer_id:
                return RefundProcessorReturnMessage(success=False, message=f"No customer found for order {order_id}")
            
            # Get completed payments with priority (Razorpay first, then wallet)
            all_payments = self.payment_repository.get_payment_details_by_order_id(order_id)
            completed_payments = []
            for payment in all_payments:
                if payment.get('payment_status') == PaymentStatus.COMPLETED:
                    completed_payments.append(payment)

            if not completed_payments:
                return RefundProcessorReturnMessage(success=False, message=f"No completed payments found for order {order_id}")
            
            # Get already refunded amounts per payment_id
            refunded_amounts = self.refund_repository.get_refunded_amounts_by_order_id(order_id)
            logger.info(f"[REFUND_PROCESSOR] Already refunded amounts: {refunded_amounts}")
            
            # Calculate remaining refundable amount for each payment
            available_payments = []
            for payment in completed_payments:
                payment_id = payment.get('id')
                payment_amount = Decimal(str(payment.get('payment_amount', 0)))
                already_refunded = refunded_amounts.get(payment_id, Decimal('0'))
                remaining_amount = payment_amount - already_refunded
                
                if remaining_amount > 0:
                    payment['remaining_refundable'] = remaining_amount
                    available_payments.append(payment)
                    logger.info(f"[REFUND_PROCESSOR] Payment {payment_id}: Original={payment_amount}, Refunded={already_refunded}, Remaining={remaining_amount}")
            
            if not available_payments:
                return RefundProcessorReturnMessage(success=False, message=f"No refundable amount remaining for order {order_id}")
            
            # Sort payments by priority: Razorpay first, then wallet/cash
            available_payments.sort(key=lambda p: (0 if p.get('payment_mode', '').lower() == 'razorpay' else 1))

            # Process refunds with priority and remaining amounts
            remaining_refund = total_refund_needed
            refund_results = []
            
            for payment in available_payments:
                if remaining_refund <= 0:
                    break
                    
                remaining_refundable = payment['remaining_refundable']
                refund_amount = min(remaining_refund, remaining_refundable)
                
                if refund_amount > 0:
                    logger.info(f"[REFUND_PROCESSOR] Processing refund of {refund_amount} from payment {payment.get('id')} (Remaining refundable: {remaining_refundable})")
                    
                    payment_mode = payment.get('payment_mode', '').lower()
                    
                    if payment_mode == 'razorpay':
                        refund_result = self._process_razorpay_refund(payment, order_id, float(refund_amount))
                    elif payment_mode in ['cash', 'wallet', 'cod']:
                        refund_result = self._process_wallet_refund(payment, order_id, customer_id, float(refund_amount))
                    else:
                        logger.warning(f"[REFUND_PROCESSOR] Unsupported payment mode: {payment_mode}")
                        continue
                    
                    if refund_result.success:
                        remaining_refund -= refund_amount
                        refund_results.append(refund_result.data)
                        logger.info(f"[REFUND_PROCESSOR] Successfully processed refund of {refund_amount}, remaining to refund: {remaining_refund}")
                    else:
                        logger.error(f"[REFUND_PROCESSOR] Refund failed: {refund_result.message}")
            
            # Determine success and prepare final result
            success = remaining_refund <= 0
            final_result = {
                "success": success,
                "order_id": order_id,
                "total_refund_needed": float(total_refund_needed),
                "total_refund_processed": float(total_refund_needed - remaining_refund),
                "remaining_refund": float(remaining_refund),
                "returns_processed": returns_to_process,
                "refund_results": refund_results,
                "message": "Return quantity refunds processed successfully" if success else "Partial return refund processing completed",
                "processed_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Update return refund status based on refund success
            if success:
                # Mark all returns as completed since all refunds were successful
                for return_data in returns_to_process.values():
                    self.order_repository.update_return_refund_status(return_data['return_id'], 'completed')
                logger.info(f"[REFUND_PROCESSOR] Updated refund status to completed for {len(returns_to_process)} returns")
            else:
                # Mark returns as partial if some refunds failed
                for return_data in returns_to_process.values():
                    self.order_repository.update_return_refund_status(return_data['return_id'], 'partial')
                logger.info(f"[REFUND_PROCESSOR] Updated refund status to partial for {len(returns_to_process)} returns")
            
            return RefundProcessorReturnMessage(success=success, message=final_result["message"], data=final_result)
            
        except Exception as e:
            logger.error(f"[REFUND_PROCESSOR] Error processing return quantity refunds for order {order_id}: {str(e)}")
            return RefundProcessorReturnMessage(
                success=False,
                message=f"Failed to process return quantity refunds: {str(e)}",
                refund_details=[]
            )

    def _process_razorpay_refund(self, payment: Dict[str, Any], order_id: str, amount: float) -> RefundProcessorReturnMessage:
        """Process Razorpay refund - supports both full and partial refunds"""
        try:

            payment_internal_id = payment.get('id')
            razorpay_payment_id = payment.get('payment_id')
            
            # Use provided amount or full payment amount
            refund_amount = float(amount)
            logger.info(f"Processing Razorpay refund | payment_id={razorpay_payment_id} | amount={refund_amount}")

            # Create refund with Razorpay
            razorpay_result = self.razorpay_service.create_refund(razorpay_payment_id, {"notes": f"Refund for cancelled items - Order {order_id}"}, refund_amount)

            if razorpay_result.get('success'):
                refund_data = razorpay_result.get('refund')

                # Store refund record and immediately update statuses
                refund_record = self.refund_repository.create_refund_record(
                    payment_id=payment_internal_id,
                    refund_id=refund_data.get('id'),
                    refund_amount=refund_amount,
                    refund_status=RefundStatus.CREATED
                )
    
                self.refund_repository.update_refund_status(refund_data.get('id'), RefundStatus.PROCESSED)
                logger.info(f"[GRN_REFUND] Updated refund status for: {refund_data.get('id')}")
                
                return RefundProcessorReturnMessage(success=True, message="Razorpay refund processed", data={
                    "refund_id": refund_record,
                    "razorpay_refund_id": refund_data.get('id'),
                    "refund_amount": refund_amount,
                    "payment_id": razorpay_payment_id
                })
            else:
                return RefundProcessorReturnMessage(success=False, message=f"Razorpay refund failed: {razorpay_result.message}")
                
        except Exception as e:
            logger.error(f"Razorpay refund error | payment_id={payment.get('payment_id')} | error={e}", exc_info=True)
            return RefundProcessorReturnMessage(success=False, message=f"Error processing Razorpay refund: {str(e)}")

    def _process_wallet_refund(self, payment: Dict[str, Any], order_id: str, customer_id: str, amount: Optional[float] = None) -> RefundProcessorReturnMessage:
        """Process refund to wallet for cash/wallet payments - supports both full and partial refunds"""
        try:
            if not self.wallet_service:
                return RefundProcessorReturnMessage(
                    success=False,
                    message="Wallet service not available",
                    data={"payment_id": payment.get('payment_id'), "payment_mode": payment.get('payment_mode')}
                )

            payment_id = payment.get('payment_id')
            payment_mode = payment.get('payment_mode')

            # Use provided amount or full payment amount
            if amount is not None:
                refund_amount = float(amount)
                logger.info(f"Processing partial wallet refund | payment_id={payment_id} | amount={refund_amount}")
            else:
                refund_amount = float(payment.get('payment_amount'))
                logger.info(f"Processing full wallet refund | payment_id={payment_id} | amount={refund_amount}")

            # Add wallet entry as credit (refund)
            wallet_result = self.wallet_service.add_wallet_entry(
                customer_id=str(customer_id),
                amount=refund_amount,
                order_id=order_id,
                payment_id=payment_id,
                entry_type="credit",
                reference_type="order_refund",
                description=f"Refund for {payment_mode} payment - Order {order_id}"
            )

            if not wallet_result.success:
                return RefundProcessorReturnMessage(
                    success=False,
                    message=wallet_result.message,
                    data={"payment_id": payment_id, "payment_mode": payment_mode}
                )

            # Generate refund ID for wallet refunds
            transaction_id = wallet_result.data.get('transaction_id')
            wallet_refund_id = f"wallet_refund_{transaction_id if transaction_id else payment_id}"

            # Create refund record in database
            self.refund_repository.create_refund_record(payment.get('id'), wallet_refund_id, refund_amount)

            # Update refund status only
            self.refund_repository.update_refund_status(wallet_refund_id, RefundStatus.PROCESSED)

            logger.info(f"[GRN_REFUND] Created wallet refund: {wallet_refund_id} for amount: {refund_amount} to customer: {customer_id}")

            result = {
                "refund_id": wallet_refund_id,
                "refund_amount": float(refund_amount),
                "refund_status": RefundStatus.get_description(RefundStatus.PROCESSED),
                "payment_id": payment_id,
                "payment_mode": payment_mode,
                "refund_method": "wallet_credit",
                "customer_id": customer_id,
                "wallet_transaction_id": wallet_result.data.get('transaction_id'),
                "wallet_balance_after": wallet_result.data.get('balance_after')
            }
            return RefundProcessorReturnMessage(success=True, message="Wallet refund processed successfully", data=result)

        except Exception as e:
            logger.error(f"Wallet refund error for payment {payment.get('payment_id')}: {e}")
            return RefundProcessorReturnMessage(
                success=False,
                message=f"Error processing wallet refund: {str(e)}",
                data={"payment_id": payment.get('payment_id'), "payment_mode": payment.get('payment_mode')}
            )
