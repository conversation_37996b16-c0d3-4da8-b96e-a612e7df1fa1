from datetime import datetime
import os
from zoneinfo import ZoneInfo


def _get_tz() -> ZoneInfo:
    tz_name = os.getenv('APP_TIME_ZONE', 'Asia/Kolkata')
    return ZoneInfo(tz_name)


def get_current_time() -> datetime:
    return datetime.now(_get_tz())


def to_tz(dt: datetime) -> datetime:
    tz = _get_tz()
    if dt.tzinfo is None:
        return dt.replace(tzinfo=tz)
    return dt.astimezone(tz)


def format_datetime_to_string(dt: datetime, fmt: str = '%Y-%m-%d %H:%M:%S') -> str:
    return to_tz(dt).strftime(fmt)
