# Types
from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime, timezone

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_repository')

# Database wrapper
from core.repository.main import OMSDatabase


class RefundRepository:
    """Repository for refund operations on OMS database"""

    def __init__(self):
        self.db = OMSDatabase()

    def create_refund_record(
        self,
        payment_id: int,
        refund_id: str,
        refund_amount: Decimal,
        refund_status: int = 60  # RefundStatus.CREATED
    ) -> int:
        """Create a refund record in OMS database"""
        query = """
            INSERT INTO refund_details (
                payment_id, refund_id, refund_amount, refund_status,
                created_at, updated_at
            ) VALUES (
                %(payment_id)s, %(refund_id)s, %(refund_amount)s, %(refund_status)s,
                NOW(), NOW()
            ) RETURNING id
        """

        params = {
            'payment_id': payment_id,
            'refund_id': refund_id,
            'refund_amount': refund_amount,
            'refund_status': refund_status
        }

        return self.db.execute_insert(query, params)

    def update_refund_status(self, refund_id: str, new_status: int, refund_date: datetime = None) -> bool:
        """Update refund status by refund_id"""
        query = """
            UPDATE refund_details 
            SET refund_status = %(new_status)s, updated_at = NOW()
            WHERE refund_id = %(refund_id)s
        """
        
        params = {
            'refund_id': refund_id,
            'new_status': new_status
        }
        
        if refund_date:
            query = """
                UPDATE refund_details 
                SET refund_status = %(new_status)s, refund_date = %(refund_date)s, updated_at = NOW()
                WHERE refund_id = %(refund_id)s
            """
            params['refund_date'] = refund_date
        
        affected_rows = self.db.execute_update(query, params)
        return affected_rows > 0

    def get_total_refunded_amount_by_payment_id(self, payment_id: int) -> Decimal:
        """Get total refunded amount for a specific payment_id"""
        query = """
            SELECT COALESCE(SUM(refund_amount), 0) as total_refunded
            FROM refund_details 
            WHERE payment_id = %(payment_id)s
            AND refund_status IN (70, 80)  -- COMPLETED, PROCESSED statuses
        """
        
        result = self.db.fetch_one(query, {'payment_id': payment_id})
        return Decimal(str(result.get('total_refunded', 0))) if result else Decimal('0')

    def get_refund_by_id(self, refund_id: str) -> Optional[Dict]:
        """Get refund record by refund_id"""
        query = """
            SELECT r.id, r.payment_id, r.refund_id, r.refund_amount, r.refund_status, 
                   r.refund_date, r.created_at, r.updated_at, p.payment_id, p.order_id 
            FROM refund_details r
            JOIN payment_details p ON r.payment_id = p.id
            WHERE r.refund_id = %s
        """
        return self.db.fetch_one(query, (refund_id,))

    def get_refunds_for_payment(self, payment_id: int) -> List[Dict]:
        """Get all refunds for a payment"""
        query = """
            SELECT id, payment_id, refund_id, refund_amount, refund_status, refund_date, created_at, updated_at
            FROM refund_details 
            WHERE payment_id = %s
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (payment_id,))

    def get_refunded_amounts_by_order_id(self, order_id: str) -> Dict[int, Decimal]:
        """Get total refunded amounts grouped by payment_id for an order"""
        query = """
            SELECT pd.id as payment_id, COALESCE(SUM(rd.refund_amount), 0) as total_refunded
            FROM payment_details pd
            JOIN orders o ON pd.order_id = o.id
            LEFT JOIN refund_details rd ON pd.id = rd.payment_id 
                AND rd.refund_status in (61, 62) -- Pending, Processed statuses
            WHERE o.order_id = %(order_id)s
            GROUP BY pd.id
        """

        results = self.db.execute_query(query, {'order_id': order_id})
        result_dict = {}
        for result in results:
            result_dict[result['payment_id']] =  Decimal(str(result.get('total_refunded', 0)))
        return result_dict

