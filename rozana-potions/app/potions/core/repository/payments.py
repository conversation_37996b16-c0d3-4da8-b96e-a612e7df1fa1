from typing import Dict, Any, List, Optional

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('payment_repository')

# Database wrapper
from core.repository.main import OMSDatabase


class PaymentRepository:
    """Repository for payment operations on OMS database"""

    def __init__(self):
        """Initialize database connection"""
        self.db = OMSDatabase()

    def get_payment_details_by_payment_id(self, payment_id: str) -> Optional[Dict]:
        """Get payment record by external payment_id"""
        query = """
            SELECT * FROM payment_details 
            WHERE payment_id = %s
        """
        return self.db.fetch_one(query, (payment_id,))

    def get_payment_details_by_internal_order_id(self, internal_order_id: int) -> List[Dict]:
        """Get all payment records for an internal order"""
        query = """
            SELECT * FROM payment_details 
            WHERE order_id = %s
            ORDER BY created_at DESC
        """
        return self.db.execute_query(query, (internal_order_id,))

    def get_payment_details_by_order_id(self, order_id: str) -> Optional[Dict]:
        """Get payment record by order_id"""
        query = """
            SELECT pd.id, pd.order_id, pd.payment_order_id, pd.payment_id, pd.payment_amount, 
            pd.payment_date, pd.payment_mode, pd.payment_status, pd.total_amount 
            FROM payment_details AS pd
            JOIN orders AS o ON pd.order_id = o.id
            WHERE o.order_id = %s 
        """
        return self.db.execute_query(query, (order_id,))

    def get_payment_details_by_order_id_and_payment_mode(self, order_id: str, payment_mode: str) -> Optional[Dict]:
        """Get payment record for an order and payment mode"""
        query = """
            SELECT pd.id, pd.order_id, pd.payment_order_id, pd.payment_id, pd.payment_amount,
            pd.payment_date, pd.payment_mode, pd.payment_status, pd.total_amount
            FROM payment_details AS pd
            JOIN orders AS o ON pd.order_id = o.id
            WHERE o.order_id = %s AND pd.payment_mode = %s
            ORDER BY pd.created_at DESC
        """
        return self.db.fetch_one(query, (order_id, payment_mode))

    def update_payment_details_status(self, payment_id: str, new_status: int) -> bool:
        """Update payment status"""
        query = """
            UPDATE payment_details 
            SET payment_status = %(new_status)s, updated_at = NOW()
            WHERE payment_id = %(payment_id)s
        """
        affected_rows = self.db.execute_update(query, {
            'payment_id': payment_id,
            'new_status': new_status
        })
        return affected_rows > 0

    def get_cod_amount_by_order_reference(self, order_reference: str) -> float:
        """Get COD amount for order by order reference"""
        query = """
            SELECT COALESCE(SUM(pd.payment_amount), 0) as cod_amount
            FROM payment_details pd
            JOIN orders o ON pd.order_id = o.id
            WHERE o.order_id = %s AND lower(pd.payment_mode) = 'cod'
        """
        result = self.db.fetch_one(query, (order_reference,))
        return float(result.get('cod_amount', 0)) if result else 0.0

    def get_payment_details_by_order_id(self, order_id: str) -> Optional[Dict]:
        """Get payment record by order_id"""
        query = """
            SELECT pd.id, pd.order_id, pd.payment_order_id, pd.payment_id, pd.payment_amount, 
            pd.payment_date, pd.payment_mode, pd.payment_status, pd.total_amount 
            FROM payment_details AS pd
            JOIN orders AS o ON pd.order_id = o.id
            WHERE o.order_id = %s
        """
        return self.db.execute_query(query, (order_id,))

payment_repository = PaymentRepository()
