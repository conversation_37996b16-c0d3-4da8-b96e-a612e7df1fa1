from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings

from core.repository.orders import order_repository
from integrations.tasks.forward_consignment import create_forward_consignment_task
from integrations.tasks.order_refunds import process_cancelled_and_unfulfilled_quantity_refunds_task
from payments.constants import OrderStatus

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('order_update_api')

class OrderUpdateAPIView(APIView):

    def post(self, request, wms_integration_name: str):
        try:
            facility_name = request.data.get('warehouse')
            data = request.data.get('data', {})
            order_id = data.get('order_reference')
            items = data.get('items', [])
            logger.info(f"order_update_received | wms_integration={wms_integration_name} | order_id={order_id} | items_count={len(items) if isinstance(items, list) else 0}")

            if not order_id or not items:
                logger.warning("order_update_validation_failed | reason=missing_order_id_or_items")
                return Response({'error': 'Missing order_id or items'}, status=status.HTTP_400_BAD_REQUEST)

            for item in items:
                sku_code = item.get('sku_code')
                unfulfilled_quantity = item.get('unfulfilled_quantity')
                cancelled_quantity = item.get('cancelled_quantity')
                order_quantity = item.get('order_quantity')
                full_cancel_flag = False
                if order_quantity == cancelled_quantity:
                    full_cancel_flag = True

                # Update unfulfilled quantity
                if sku_code and unfulfilled_quantity is not None:
                    updated = order_repository.update_order_item_unfulfilled_quantity(
                        order_id=order_id,
                        sku_code=sku_code,
                        unfulfilled_quantity=float(unfulfilled_quantity)
                    )
                    if updated:
                        logger.info(f"order_update_item_updated | order_id={order_id} | sku={sku_code} | unfulfilled_qty={unfulfilled_quantity}")
                    else:
                        logger.warning(f"order_update_no_rows_affected | order_id={order_id} | sku={sku_code}")
                else:
                    logger.warning(f"order_update_item_skipped | order_id={order_id} | sku={sku_code} | reason=missing_sku_or_unfulfilled_quantity")

                # Update cancelled quantity 
                if sku_code and cancelled_quantity is not None:
                    updated = order_repository.update_order_item_wh_sku_cancelled_quantity(
                        order_id=order_id,
                        sku_code=sku_code,
                        cancelled_quantity=float(cancelled_quantity),
                        new_status=OrderStatus.CANCELLED if full_cancel_flag else None
                    )
                    if updated:
                        logger.info(f"order_update_item_updated | order_id={order_id} | sku={sku_code} | cancelled_qty={cancelled_quantity}")
                    else:
                        logger.warning(f"order_update_no_rows_affected | order_id={order_id} | sku={sku_code}")


            # Initiate the refund for cancelled and unfulfilled items
            process_cancelled_and_unfulfilled_quantity_refunds_task.apply_async(args=[order_id, facility_name])

            # Create forward consignment
            order_status = data.get('order_status')
            warehouse = data.get('warehouse')
            task_id = None
            if order_status.lower() in ['invoiced', 'customer_invoices']:
                task = create_forward_consignment_task.apply_async(args=[data.get('order_reference') or order_id, warehouse, None, wms_integration_name, request.data])
                task_id = task.id
                logger.info(f"order_update_forward_cn_enqueued | order_id={order_id} | task_id={task_id}")

            return Response({'success': True, 'order_id': order_id, 'forward_cn_task_id': task_id}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.exception(f"order_update_unexpected_error | order_id={locals().get('order_id', None)} | error={str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
