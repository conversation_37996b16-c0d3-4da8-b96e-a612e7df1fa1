from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.conf import settings

from core.repository.orders import order_repository
from integrations.tasks.forward_consignment import create_forward_consignment_task

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('order_update_trigger_api')


class OrderUpdateTriggerAPIView(APIView):
    """
    API endpoint for manually triggering order update callback processing.
    WMS can call this endpoint with the same payload structure as order_update webhook.
    """

    def post(self, request, wms_integration_name: str):
        try:
            data = request.data.get('data', {})
            order_id = data.get('order_id') or data.get('order_reference')
            items = data.get('items', [])
            logger.info(f"order_update_trigger_received | wms_integration={wms_integration_name} | order_id={order_id} | items_count={len(items) if isinstance(items, list) else 0}")

            if not order_id:
                logger.warning("order_update_trigger_validation_failed | reason=missing_order_id")
                return Response({'error': 'Missing order_id or order_reference'}, status=status.HTTP_400_BAD_REQUEST)

            # Update unfulfilled quantities if items are provided
            items_updated = 0
            if items:
                for item in items:
                    sku_code = item.get('sku_code')
                    unfulfilled_quantity = item.get('unfulfilled_quantity')
                    
                    if sku_code and unfulfilled_quantity is not None:
                        updated = order_repository.update_order_item_unfulfilled_quantity(
                            order_id=order_id,
                            sku_code=sku_code,
                            unfulfilled_quantity=float(unfulfilled_quantity)
                        )
                        if updated:
                            items_updated += 1
                            logger.info(f"order_update_trigger_item_updated | order_id={order_id} | sku={sku_code} | unfulfilled_qty={unfulfilled_quantity}")
                        else:
                            logger.warning(f"order_update_trigger_no_rows_affected | order_id={order_id} | sku={sku_code}")
                    else:
                        logger.warning(f"order_update_trigger_item_skipped | order_id={order_id} | sku={sku_code} | reason=missing_sku_or_unfulfilled_quantity")

            # Check if forward CN should be created from order_update
            task_id = None
            if getattr(settings, 'TMS_FORWARD_CN_FROM_ORDER_UPDATE_ENABLED', True):
                order_status = data.get('order_status') or data.get('status')
                if (order_status or '').lower() in ['invoiced', 'customer_invoices', '5']:
                    try:
                        warehouse = request.data.get('warehouse', '')
                        task = create_forward_consignment_task.apply_async(
                            args=[data.get('order_reference') or order_id, warehouse, None, wms_integration_name, request.data],
                            countdown=5
                        )
                        task_id = task.id
                        logger.info(f"order_update_trigger_forward_cn_enqueued | order_id={order_id} | task_id={task_id}")
                    except Exception as task_err:
                        logger.error(f"order_update_trigger_forward_cn_enqueue_failed | order_id={order_id} | error={task_err}")

            return Response({
                'success': True, 
                'order_id': order_id,
                'items_updated': items_updated,
                'forward_cn_task_id': task_id,
                'message': 'Order update trigger processed successfully'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.exception(f"order_update_trigger_unexpected_error | order_id={locals().get('order_id', None)} | error={str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
