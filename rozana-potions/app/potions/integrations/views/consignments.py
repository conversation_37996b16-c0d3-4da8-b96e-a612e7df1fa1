
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, serializers
from integrations.services.tms.consignment_service import ForwardCNService, ReverseCNService, TrackCNService, TMSAPIError

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('consignments_api')


# Serializers for validation
class DestinationDetailsSerializer(serializers.Serializer):
    """Serializer for destination details in forward consignment."""
    company_name = serializers.CharField(max_length=255)
    name = serializers.CharField(max_length=255)
    phone = serializers.CharField(max_length=20)
    address_line_1 = serializers.CharField(max_length=500)
    pincode = serializers.CharField(max_length=10)
    district = serializers.CharField(max_length=100)
    city = serializers.CharField(max_length=100)
    state = serializers.CharField(max_length=100)
    country = serializers.CharField(max_length=100)
    latitude = serializers.FloatField(required=False)
    longitude = serializers.FloatField(required=False)


class PieceDetailSerializer(serializers.Serializer):
    """Serializer for piece details in forward consignment."""
    description = serializers.CharField(max_length=500, required=False, allow_blank=True)
    quantity = serializers.IntegerField(min_value=1)
    declared_value = serializers.FloatField(min_value=0)
    piece_product_code = serializers.CharField(max_length=100)
    cod_amount = serializers.FloatField(min_value=0)
    reference_image_url = serializers.URLField(required=False, allow_blank=True)


class ForwardConsignmentSerializer(serializers.Serializer):
    """Serializer for forward consignment creation."""
    customer_code = serializers.CharField(max_length=100)
    reference_number = serializers.CharField(max_length=100)
    customer_reference_number = serializers.CharField(max_length=100)
    hub_code = serializers.CharField(max_length=50)
    cod_amount = serializers.FloatField(min_value=0)
    destination_details = DestinationDetailsSerializer()
    pieces_detail_list = PieceDetailSerializer(many=True)
    cod_favor_of = serializers.CharField(max_length=100, default="Rozana")
    cod_collection_mode = serializers.CharField(max_length=50, default="", allow_blank=True)


class OriginDetailsSerializer(serializers.Serializer):
    """Serializer for origin details in reverse consignment."""
    name = serializers.CharField(max_length=255)
    phone = serializers.CharField(max_length=20)
    address_line_1 = serializers.CharField(max_length=500)
    pincode = serializers.CharField(max_length=10)
    district = serializers.CharField(max_length=100)
    city = serializers.CharField(max_length=100)
    state = serializers.CharField(max_length=100)
    country = serializers.CharField(max_length=100)
    latitude = serializers.FloatField(required=False)
    longitude = serializers.FloatField(required=False)


class ReversePieceDetailSerializer(serializers.Serializer):
    """Serializer for piece details in reverse consignment."""
    description = serializers.CharField(max_length=500, required=False, allow_blank=True)
    return_quantity = serializers.IntegerField(min_value=1)
    declared_value = serializers.FloatField(min_value=0)
    piece_product_code = serializers.CharField(max_length=100)


class ReverseConsignmentSerializer(serializers.Serializer):
    """Serializer for reverse consignment creation."""
    customer_reference_number = serializers.CharField(max_length=100)
    hub_code = serializers.CharField(max_length=50)
    hub_address_line_1 = serializers.CharField(max_length=500)
    origin_details = OriginDetailsSerializer()
    pieces_detail_list = ReversePieceDetailSerializer(many=True)
    return_code = serializers.CharField(max_length=100, default="", allow_blank=True)


class ReverseConsignmentByReferenceSerializer(serializers.Serializer):
    """Serializer to trigger reverse consignment creation by return reference only."""
    return_reference = serializers.CharField(max_length=100)


class TrackConsignmentSerializer(serializers.Serializer):
    """Serializer for consignment tracking."""
    reference_number = serializers.CharField(max_length=100)


# API Views
class ForwardConsignmentAPIView(APIView):
    """
    API View for creating forward consignment notes (delivery).
    """
    
    def post(self, request):
        """
        Create a forward consignment note via TMS.
        
        This endpoint receives order data from OMS and creates a consignment note
        in the TMS (Shipsy) system for delivery from warehouse to customer.
        """
        try:
            # Validate request data
            serializer = ForwardConsignmentSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'error': 'Validation failed',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            
            # Initialize forward consignment service
            forward_service = ForwardCNService()
            
            # Create forward consignment via TMS
            logger.info(f"Creating forward consignment for reference: {validated_data['reference_number']}")
            
            tms_response = forward_service.create(
                customer_code=validated_data['customer_code'],
                reference_number=validated_data['reference_number'],
                customer_reference_number=validated_data['customer_reference_number'],
                hub_code=validated_data['hub_code'],
                cod_amount=validated_data['cod_amount'],
                destination_details=validated_data['destination_details'],
                pieces_detail_list=validated_data['pieces_detail_list'],
                cod_favor_of=validated_data.get('cod_favor_of', 'Rozana'),
                cod_collection_mode=validated_data.get('cod_collection_mode', '')
            )
            
            logger.info(f"Forward consignment created successfully: {validated_data['reference_number']}")
            
            return Response({
                'success': True,
                'message': 'Forward consignment note created successfully',
                'reference_number': validated_data['reference_number'],
                'tms_response': tms_response
            }, status=status.HTTP_201_CREATED)
            
        except TMSAPIError as e:
            logger.error(f"TMS API error: {str(e)}")
            return Response({
                'success': False,
                'error': 'TMS API error',
                'details': str(e)
            }, status=status.HTTP_502_BAD_GATEWAY)
            
        except Exception as e:
            logger.error(f"Unexpected error in ForwardConsignmentAPIView: {str(e)}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ReverseConsignmentByReferenceAPIView(APIView):
    """
    API View to accept a return_reference, fetch data from OMS, and create a reverse consignment via TMS.
    """

    def post(self, request):
        try:
            serializer = ReverseConsignmentByReferenceSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'error': 'Validation failed',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

            return_reference = serializer.validated_data['return_reference']

            reverse_service = ReverseCNService()
            prepared = reverse_service.prepare_from_oms(return_reference=return_reference)
            tms_response = reverse_service.create(
                customer_reference_number=prepared['customer_reference_number'],
                hub_code=prepared['hub_code'],
                hub_address_line_1=prepared['hub_address_line_1'],
                origin_details=prepared['origin_details'],
                pieces_detail_list=prepared['pieces_details'],
                return_code=prepared['return_code'],
            )

            return Response({
                'success': True,
                'message': 'Reverse consignment note created successfully',
                'return_reference': return_reference,
                'tms_response': tms_response
            }, status=status.HTTP_201_CREATED)

        except TMSAPIError as e:
            logger.error(f"TMS API error: {str(e)}")
            return Response({
                'success': False,
                'error': 'TMS API error',
                'details': str(e)
            }, status=status.HTTP_502_BAD_GATEWAY)
        except Exception as e:
            logger.error(f"Unexpected error in ReverseConsignmentByReferenceAPIView: {str(e)}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ReverseConsignmentAPIView(APIView):
    """
    API View for creating reverse consignment notes (returns).
    """
    
    def post(self, request):
        """
        Create a reverse consignment note via TMS.
        
        This endpoint receives return order data from OMS and creates a reverse consignment note
        in the TMS (Shipsy) system for product returns from customer to warehouse.
        """
        try:
            # Validate request data
            serializer = ReverseConsignmentSerializer(data=request.data)
            if not serializer.is_valid():
                logger.error(f"Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'error': 'Validation failed',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            
            # Initialize reverse consignment service
            reverse_service = ReverseCNService()
            
            # Create reverse consignment via TMS
            logger.info(f"Creating reverse consignment for reference: {validated_data['customer_reference_number']}")
            
            tms_response = reverse_service.create(
                customer_reference_number=validated_data['customer_reference_number'],
                hub_code=validated_data['hub_code'],
                hub_address_line_1=validated_data['hub_address_line_1'],
                origin_details=validated_data['origin_details'],
                pieces_detail_list=validated_data['pieces_detail_list'],
                return_code=validated_data.get('return_code', '')
            )
            
            logger.info(f"Reverse consignment created successfully: {validated_data['customer_reference_number']}")
            
            return Response({
                'success': True,
                'message': 'Reverse consignment note created successfully',
                'customer_reference_number': validated_data['customer_reference_number'],
                'tms_response': tms_response
            }, status=status.HTTP_201_CREATED)
            
        except TMSAPIError as e:
            logger.error(f"TMS API error: {str(e)}")
            return Response({
                'success': False,
                'error': 'TMS API error',
                'details': str(e)
            }, status=status.HTTP_502_BAD_GATEWAY)
            
        except Exception as e:
            logger.error(f"Unexpected error in ReverseConsignmentAPIView: {str(e)}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ConsignmentTrackingAPIView(APIView):
    """
    API View for tracking consignment status.
    """
    
    def get(self, request):
        """
        Track a consignment by reference number.
        
        This endpoint tracks the status of a consignment using the TMS tracking API.
        """
        try:
            # Validate request data
            serializer = TrackConsignmentSerializer(data=request.query_params)
            if not serializer.is_valid():
                logger.error(f"Validation errors: {serializer.errors}")
                return Response({
                    'success': False,
                    'error': 'Validation failed',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = serializer.validated_data
            
            # Initialize tracking service
            track_service = TrackCNService()
            
            # Track consignment via TMS
            logger.info(f"Tracking consignment for reference: {validated_data['reference_number']}")
            
            tracking_response = track_service.track(
                reference_number=validated_data['reference_number']
            )
            
            return Response({
                'success': True,
                'reference_number': validated_data['reference_number'],
                'tracking_data': tracking_response
            }, status=status.HTTP_200_OK)
            
        except TMSAPIError as e:
            logger.error(f"TMS API error: {str(e)}")
            return Response({
                'success': False,
                'error': 'TMS API error',
                'details': str(e)
            }, status=status.HTTP_502_BAD_GATEWAY)
            
        except Exception as e:
            logger.error(f"Unexpected error in ConsignmentTrackingAPIView: {str(e)}")
            return Response({
                'success': False,
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)