import json
from typing import Dict, Any, List, Optional
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

from integrations.services.wms.sales_return_service import SalesReturnService
from integrations.services.wms.auth_service import WMSAPIError
from potions.logging.utils import get_app_logger

logger = get_app_logger('sale_return')


def fetch_sales_return_data_from_oms(return_reference: str) -> Optional[Dict[str, Any]]:
    """
    Fetch sales return data from OMS using the service layer
    
    Args:
        return_reference: Sales return reference ID
        
    Returns:
        Sales return data from OMS or None if not found
    """
    sales_return_service = SalesReturnService()
    return sales_return_service.fetch_sales_return_data_from_oms(return_reference)


def process_sales_return_to_wms(return_reference: str, warehouse: str) -> Optional[Dict[str, Any]]:
    """
    Process sales return from OMS to WMS
    """
    try:
        logger.info(f"Processing sales return {return_reference} to WMS")
        service = SalesReturnService()
        
        # Fetch data from OMS using the existing function
        oms_data = fetch_sales_return_data_from_oms(return_reference)
        if not oms_data:
            error_msg = f"Sales return {return_reference} not found in OMS"
            logger.error(error_msg)
            return {"error": error_msg, "step": "oms_fetch"}
        # Validate there are items to send
        if not oms_data.get('items'):
            error_msg = f"Sales return {return_reference} has no items; skipping WMS call"
            logger.warning(error_msg)
            return {"error": error_msg, "step": "validation", "oms_data": oms_data}
        
        # Send to WMS
        try:
            wms_response = service.send_sales_return_to_wms(oms_data, warehouse)
        except WMSAPIError as we:
            error_msg = f"Failed to send sales return {return_reference} to WMS"
            logger.error(f"{error_msg}: {we}")
            
            # Try to parse WMS error response for duplicate return reference
            wms_body = getattr(we, "response_body", None)
            wms_errors = []
            wms_message = ""
            
            if wms_body:
                try:
                    wms_response = json.loads(wms_body)
                    wms_errors = wms_response.get("errors", [])
                    wms_message = wms_response.get("message", "")
                except (json.JSONDecodeError, TypeError):
                    pass
            
            return {
                "error": error_msg,
                "step": "wms_send",
                "wms_status": getattr(we, "status_code", None),
                "wms_body": wms_body,
                "wms_errors": wms_errors,
                "wms_message": wms_message,
                "wms_url": getattr(we, "url", None),
                "oms_data": oms_data,
            }
        
        # Extract return IDs and message from WMS response
        return_ids = wms_response.get("return_ids", [])
        wms_message = wms_response.get("message", "Sales return processed successfully")
        wms_errors = wms_response.get("errors", [])
        
        # Update OMS status
        success = service.update_oms_status(return_reference, "synced", wms_response)
        if not success:
            logger.warning(f"Failed to update OMS status for {return_reference}")
        
        logger.info(f"Successfully processed sales return {return_reference}")
        return {
            "return_reference": return_reference,
            "wms_return_ids": return_ids,
            "wms_message": wms_message,
            "wms_errors": wms_errors,
            "wms_response": wms_response,
            "oms_updated": success
        }
        
    except Exception as e:
        error_msg = f"Error processing sales return {return_reference}: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg, "step": "exception"}


@method_decorator(csrf_exempt, name='dispatch')
class SalesReturnView(View):
    """
    Django view for handling sales return requests
    """
    
    def __init__(self):
        super().__init__()
        self.sales_return_service = SalesReturnService()
    
    def post(self, request):
        """
        Handle POST request to process a sales return
        Expected payload: {"return_reference": "XMAX8610"}
        """
        try:

            data = json.loads(request.body)
            return_reference = data.get('return_reference')
            warehouse = (request.headers.get('warehouse') or '').strip()
            
            if not return_reference:
                logger.warning("sale_return_missing_return_reference")
                return JsonResponse({
                    'success': False,
                    'error': 'Missing required field: return_reference',
                    'message': 'return_reference is required to process sales return'
                }, status=400)
            
            # Validate required header: warehouse
            if not warehouse:
                logger.warning("sale_return_missing_warehouse_header")
                return JsonResponse({
                    'success': False,
                    'error': 'Missing required header: warehouse',
                    'message': 'Provide warehouse header with a valid warehouse/hub code'
                }, status=400)
            
            # Fetch data from OMS and process to WMS
            result = process_sales_return_to_wms(return_reference, warehouse)
        
            if result and 'error' not in result:
                return JsonResponse({
                    'errors': result.get('wms_errors', []),
                    'message': result.get('wms_message', 'Sales Return Created Successfully!'),
                    'return_ids': result.get('wms_return_ids', [])
                })
            else:
                error_details = result if result else {'error': 'Unknown error', 'step': 'unknown'}
                step = error_details.get('step')
                status_code = 500
                message = f'Sales return {return_reference} processing failed'
                if step == 'oms_fetch':
                    status_code = 404
                    message = f'Sales return {return_reference} not found in OMS'
                elif step == 'validation':
                    status_code = 422
                    message = f'Sales return {return_reference} has invalid data'
                elif step == 'wms_send':
                    # Check if it's a duplicate return reference error
                    wms_errors = error_details.get('wms_errors', [])
                    if any('already available' in str(error).lower() for error in wms_errors):
                        # Return WMS format for duplicate return reference
                        return JsonResponse({
                            'errors': wms_errors,
                            'message': error_details.get('wms_message', ''),
                            'return_ids': []
                        })
                    else:
                        status_code = 502
                        message = f'WMS rejected sales return {return_reference}'
                elif step == 'exception':
                    status_code = 500
                    message = f'Unexpected error while processing {return_reference}'

                return JsonResponse({
                    'success': False,
                    'error': 'Failed to process sales return',
                    'return_reference': return_reference,
                    'details': error_details,
                    'message': message
                }, status=status_code)
            
        except json.JSONDecodeError:
            logger.warning("sale_return_invalid_json")
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON data'
            }, status=400)
            
        except Exception as e:
            logger.error(f"Unexpected error in SalesReturnView: {e}")
            return JsonResponse({
                'success': False,
                'error': 'Internal server error'
            }, status=500)

