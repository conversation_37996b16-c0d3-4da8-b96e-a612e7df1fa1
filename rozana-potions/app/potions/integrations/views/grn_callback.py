from typing import List, Optional
from decimal import Decimal

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from potions.logging.utils import get_app_logger
from core.repository.sales_return import sales_return_repository
from core.repository.orders import order_repository
from payments.refunds.refund_processor import RefundProcessor

logger = get_app_logger('grn_callback')


class GRNCallbackAPIView(APIView):
    """
    API endpoint to receive GRN (Goods Receipt Note) callbacks from WMS after a sales return.
    """ 

    def post(self, request, wms_integration_name: Optional[str] = None):
        try:
            payload = request.data or {}

            # GRN payload is at root level (no 'data' wrapper)
            # Prefer 'sales_return_reference' (e.g., RTN-...), fallback to 'sales_return_id' (e.g., SR-...)
            return_reference: Optional[str] = (
                payload.get('sales_return_reference') or payload.get('sales_return_id')
            )
            if not return_reference:
                return Response({
                    'error': 'Missing sales_return_reference or sales_return_id in payload'
                }, status=status.HTTP_400_BAD_REQUEST)

            items = payload.get('items') or []
            skus: Optional[List[str]] = None
            if isinstance(items, list) and items:
                # Extract SKU codes if provided
                skus = [str(item.get('sku_code')).strip() for item in items if item.get('sku_code')]
                if not skus:
                    skus = None
            
            # Update return_items status to 'completed'
            updated_count = sales_return_repository.update_return_items_status(
                return_reference=return_reference,
                status_value='completed',
                skus=skus,
            )

            # If any items were updated to completed, also mark the return header as completed
            header_updated = 0
            if updated_count and updated_count > 0:
                try:
                    header_updated = sales_return_repository.update_return_status(
                        return_reference=return_reference,
                        status='completed'
                    )
                    logger.info(f"Updated return header to 'completed' | reference={return_reference}, header_updated={header_updated}")
                except Exception as e:
                    logger.error(f"Failed to update return header to 'completed' for {return_reference}: {e}", exc_info=True)

            # Process refunds when items are completed
            # updated_count = 1
            refund_result = None
            if updated_count and updated_count > 0:
                refund_result = self._process_refunds_from_grn(payload, return_reference)

            logger.info(
                f"GRN callback processed for integration={wms_integration_name}, return_reference={return_reference}, items_filtered={bool(skus)}, updated_rows={updated_count}"
            )

            response_data = {
                'message': 'GRN processed successfully',
                'wms_integration_name': wms_integration_name,
                'return_reference': return_reference,
                'updated_items': updated_count,
                'updated_header': bool(header_updated),
                'filtered_by_skus': bool(skus)
            }
            
            # Add refund processing result if available
            if refund_result:
                response_data['refund_processing'] = refund_result

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error("Error processing GRN callback: %s", e, exc_info=True)
            return Response({
                'error': 'Internal server error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _process_refunds_from_grn(self, payload: dict, return_reference: str) -> dict:
        """
        Process refunds based on GRN callback payload.
        Calculate refund amount from buy_price and sales_return_quantity.
        """
        try:
            
            logger.info(f"[GRN_REFUND] Starting refund processing for return_reference: {return_reference}")
            
            # Get order_id from return_reference
            order_id = sales_return_repository.get_order_id_by_return_reference(return_reference)
            if not order_id:
                logger.error(f"[GRN_REFUND] No order found for return_reference: {return_reference}")
                return {
                    'success': False,
                    'message': f'No order found for return_reference: {return_reference}'
                }

            # Calculate total refund amount from GRN payload items
            items = payload.get('items', [])
            total_refund_amount = Decimal('0')
            processed_items = []

            for item in items:
                try:
                    sales_return_quantity = Decimal(str(item.get('sales_return_quantity', 0)))
                    sku_code = item.get('sku_code', '')
                    
                    if not sku_code or sales_return_quantity <= 0:
                        continue
                    
                    sale_price = order_repository.get_sale_price_by_sku(order_id, sku_code)
                    if sale_price is None:
                        logger.warning(f"[GRN_REFUND] No sale_price found for SKU {sku_code} in order {order_id}")
                        continue
                    
                    sale_price_decimal = Decimal(str(sale_price))
                    
                    # Calculate refund amount using actual sale_price
                    item_refund_amount = sale_price_decimal * sales_return_quantity
                    total_refund_amount += item_refund_amount
                    
                    processed_items.append({
                        'sku_code': sku_code,
                        'sale_price': float(sale_price_decimal),
                        'quantity': float(sales_return_quantity),
                        'refund_amount': float(item_refund_amount)
                    })
                    
                    logger.info(f"[GRN_REFUND] Item {sku_code}: sale_price={sale_price_decimal}, qty={sales_return_quantity}, refund={item_refund_amount}")
                    
                except (ValueError, TypeError) as e:
                    logger.warning(f"[GRN_REFUND] Error processing item {item.get('sku_code', 'unknown')}: {e}")
                    continue

            if total_refund_amount <= 0:
                logger.info(f"[GRN_REFUND] No refund amount calculated for return_reference: {return_reference}")
                return {
                    'success': True,
                    'message': 'No refund amount needed',
                    'refund_amount': 0,
                    'processed_items': processed_items
                }

            logger.info(f"[GRN_REFUND] Total refund amount calculated: {total_refund_amount} for order: {order_id}")

            # Initialize refund processor and process refunds
            refund_processor = RefundProcessor()
            refund_result = refund_processor.create_refund_from_grn_callback(
                order_id=order_id,
                refund_amount=float(total_refund_amount),
                return_reference=return_reference
            )

            result = {
                'success': refund_result.success,
                'message': refund_result.message,
                'refund_amount': float(total_refund_amount),
                'processed_items': processed_items,
                'order_id': order_id
            }

            if refund_result.data:
                result['refund_details'] = refund_result.data

            logger.info(f"[GRN_REFUND] Refund processing completed for return_reference: {return_reference}, success: {refund_result.success}")
            return result

        except Exception as e:
            logger.error(f"[GRN_REFUND] Error processing refunds for return_reference {return_reference}: {e}", exc_info=True)
            return {
                'success': False,
                'message': f'Error processing refunds: {str(e)}',
                'return_reference': return_reference
            }