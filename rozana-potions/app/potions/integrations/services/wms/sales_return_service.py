from typing import Dict, Any, Optional
from django.conf import settings
from core.repository.sales_return import sales_return_repository
from integrations.services.wms.auth_service import WMSA<PERSON>, WMSAPIError
from integrations.models import WMSIntegration
from potions.logging.utils import get_app_logger

logger = get_app_logger('sales_return_service')


class SalesReturnService:
    """
    Service for handling sales return operations with OMS and WMS integration
    """
    
    def __init__(self, wms_integration_name: str = 'default'):
        # Initialize WMS authentication from database configuration (no hardcoded secrets)
        self._init_wms_auth(wms_integration_name)
        # Document type is always hardcoded to 'order_reference' for WMS payloads.


    def _init_wms_auth(self, integration_name: str) -> None:
        """Initialize WMS authentication using integration settings from DB."""
        try:
            wms_config = WMSIntegration.objects.filter(
                name=integration_name,
                is_active=True,
            ).first()
            if not wms_config:
                wms_config = WMSIntegration.objects.filter(is_active=True).first()
            if not wms_config:
                raise ValueError('No active WMS integration found')
            self.wms_auth = WMSAuth(
                base_url=wms_config.base_url,
                client_id=wms_config.client_id,
                client_secret=wms_config.client_secret,
            )
            logger.info(
                'Initialized WMS sales return service with integration: %s',
                wms_config.name,
            )
        except Exception as exc:
            logger.error('Failed to initialize WMS authentication: %s', exc)
            raise ValueError(f'WMS authentication initialization failed: {exc}')
    
    def fetch_sales_return_data_from_oms(self, return_reference: str) -> Optional[Dict[str, Any]]:
        """
        Fetch sales return data from OMS database and format for WMS API
        
        Returns:
            Dict with required fields for WMS API:
            {
                "return_reference": str,
                "document_type": str,
                "return_type": str,
                "items": [
                    {
                        "sku_code": str,
                        "order_reference": str,
                        "return_quantity": int,
                        "rejected_quantity": int,
                        "reason": str
                    }
                ]
            }
        """
        try:
            # Fetch sales return + order details via repository
            return_data = sales_return_repository.get_return_with_order_by_reference(return_reference)
            if not return_data:
                logger.error(f"Sales return {return_reference} not found in OMS database")
                return None

            # Fetch sales return items via repository
            items_results = sales_return_repository.get_return_items_by_reference(return_reference)
            if not items_results:
                logger.error(f"No items found for return {return_reference}")
                return None

            # Build items list with only required fields
            items = []
            for item_dict in items_results:
                wms_item = {
                    # Use WMS SKU (wh_sku) instead of internal SKU code
                    "sku_code": item_dict.get("wh_sku", ""),
                    "order_reference": return_data.get("order_id", "") or return_data.get("order_reference", ""),
                    "return_quantity": int(item_dict.get("quantity_returned", 0)),
                    "rejected_quantity": 0,
                    "reason": "damaged",
                }
                # Provide order_item id as line reference for traceability when available
                order_item_id = item_dict.get("order_item_id")
                if order_item_id is not None:
                    wms_item["line_reference"] = str(order_item_id)
                items.append(wms_item)
            
            # Always use 'order_reference' as document_type for WMS
            wms_payload = {
                "return_reference": return_data.get("return_reference", ""),
                "document_type": "order_reference",  # Hardcoded to match WMS requirements
                "return_type": "CIR",
                "items": items
            }
            
            logger.debug(f"WMS Payload: {wms_payload}")
            
            logger.info(f"Formatted WMS payload for return {return_reference}")
            return wms_payload

        except Exception as e:
            logger.error(f"Error fetching sales return data from OMS: {str(e)}")
            return None

    def create_sales_return_in_wms(self, return_data: Dict[str, Any], warehouse: str) -> Dict[str, Any]:
        """
        Create a sales return in WMS
        
        Args:
            return_data: Sales return data containing return_reference, items, etc.
            warehouse: Warehouse code from orders.facility_name
            
        Returns:
            API response data
            
        Raises:
            WMSAPIError: If the API call fails
        """
        endpoint = "/api/v1/outbound/sales_return/"
        
        headers = {
            'warehouse': warehouse,
            'Content-Type': 'application/json'
        }
        
        try:
            # Validation: prevent WMS call if no items
            items = (return_data or {}).get('items') or []
            if len(items) == 0:
                msg = f"Sales return {return_data.get('return_reference', 'N/A')} has no items. Aborting WMS call."
                logger.warning(msg)
                raise WMSAPIError(msg)

            # Ensure document_type is always 'order_reference'
            if return_data:
                return_data['document_type'] = 'order_reference'
                logger.debug(f"Updated document_type to 'order_reference' for return {return_data.get('return_reference')}")
            
            logger.info(f"Sending sales return to WMS: {return_data}")
            response = self.wms_auth.make_authenticated_request(
                method='POST',
                endpoint=endpoint,
                headers=headers,
                json=return_data
            )
            
            try:
                result = response.json()
            except ValueError:
                result = {"detail": response.text}
            return result
            
        except WMSAPIError as e:
            logger.error(f"WMS API Error - Status: {getattr(e, 'status_code', 'Unknown')}, Message: {e}")
            logger.error(f"WMS API Request failed for return_reference: {return_data.get('return_reference', 'N/A')}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating sales return in WMS: {e}")
            logger.error(f"Return data: {return_data}")
            raise WMSAPIError(f"Unexpected error: {e}")

    def send_sales_return_to_wms(self, return_data: Dict[str, Any], warehouse: str) -> Optional[Dict[str, Any]]:
        """
        Send sales return data to WMS (alias for create_sales_return_in_wms)
        
        Args:
            return_data: Sales return data from OMS
            warehouse: Warehouse code
            
        Returns:
            WMS response data or None if failed
        """
        # Let exceptions bubble up so callers can surface error details
        return self.create_sales_return_in_wms(return_data, warehouse)

    def update_oms_status(self, return_reference: str, status: str, wms_response: Optional[Dict] = None) -> bool:
        """
        Update OMS status (alias for update_oms_sales_return_status)
        """
        return self.update_oms_sales_return_status(return_reference, status, wms_response)

    def update_oms_sales_return_status(self, return_reference: str, status: str, wms_response: Optional[Dict] = None) -> bool:
        """
        Update sales return status in OMS database
        
        Args:
            return_reference: Sales return reference ID
            status: New status to set
            wms_response: Optional WMS response data to store
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            affected = sales_return_repository.update_return_status(return_reference, status)
            # Always ensure items are marked 'synced' when header status target is 'synced'
            if str(status).lower() == 'synced':
                try:
                    items_updated = sales_return_repository.update_return_items_status(
                        return_reference=return_reference,
                        status_value='synced'
                    )
                    logger.info(
                        f"Ensured 'synced' status on return_items | reference={return_reference}, items_updated={items_updated} (header_affected={affected})"
                    )
                except Exception as e:
                    logger.error(
                        "Failed to update return_items to 'synced' for %s: %s",
                        return_reference,
                        e,
                        exc_info=True,
                    )
            return affected > 0

        except Exception as e:
            logger.error(f"Error updating sales return status in OMS: {str(e)}")
            return False
    
    