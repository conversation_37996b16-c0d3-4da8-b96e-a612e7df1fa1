import requests
from typing import Dict, Optional
from datetime import datetime, timedelta
from django.core.cache import cache

# Logger
from potions.logging.utils import get_app_logger
logger = get_app_logger('wms_auth')


class WMSAuthenticationError(Exception):
    """Raised when WMS authentication fails"""
    pass


class WMSAPIError(Exception):
    """Raised when WMS API calls fail"""
    def __init__(self, message: str, status_code: Optional[int] = None, response_body: Optional[str] = None, url: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_body = response_body
        self.url = url

    def __str__(self):
        base = super().__str__()
        if self.status_code is not None:
            base += f" (status={self.status_code})"
        if self.url:
            base += f" url={self.url}"
        return base


class WMSAuth:
    """
    Handles WMS authentication and token management.
    
    This class provides reusable authentication functionality for WMS API operations.
    It handles OAuth2 client credentials flow, token caching, and authenticated requests.
    """

    def __init__(self, base_url: str, client_id: str, client_secret: str):
        """
        Initialize WMS authentication handler.
        Args:
            base_url: Base URL for the WMS API (e.g., 'https://rzn1-be.stockone.com')
            client_id: OAuth2 client ID
            client_secret: OAuth2 client secret
        """
        self.base_url = base_url.rstrip('/')
        self.client_id = client_id
        self.client_secret = client_secret
        self.token_cache_key = f"wms:oauth:{self.client_id}:access_token"
        self.session = requests.Session()


    def _get_cached_token(self) -> Optional[str]:
        """
        Retrieve cached authentication token if available and not expired.
        Returns:
            Valid token string or None if not available/expired
        """
        cached_data = cache.get(self.token_cache_key)
        if cached_data:
            token, expires_at = cached_data
            if datetime.now() < expires_at:
                return token
        return None

    def _cache_token(self, token: str, expires_in: int) -> None:
        """
        Cache authentication token with expiration time.
        Args:
            token: Authentication token
            expires_in: Token lifetime in seconds
        """
        # Cache slightly less than provider TTL
        cache_duration = int(expires_in * 0.9)
        expires_at = datetime.now() + timedelta(seconds=expires_in)
        cache.set(self.token_cache_key, (token, expires_at), cache_duration)

    def authenticate(self) -> str:
        """
        Authenticate with WMS API and return access token.
        Returns:
            Access token string
        Raises:
            WMSAuthenticationError: If authentication fails
        """
        # Check for cached token first
        cached_token = self._get_cached_token()
        if cached_token:
            logger.debug("Using cached WMS token")
            return cached_token

        logger.info("Authenticating with WMS API")

        auth_url = f"{self.base_url}/o/token/"
        auth_data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'grant_type': 'client_credentials'
        }

        try:
            response = requests.post(
                auth_url,
                data=auth_data,
                timeout=30
            )
            response.raise_for_status()
            
            token_data = response.json()
            access_token = token_data.get('access_token')
            expires_in = token_data.get('expires_in', 3600)  # Default 1 hour
            
            if not access_token:
                raise WMSAuthenticationError("No access token in response")
            
            # Cache the token
            self._cache_token(access_token, expires_in)

            logger.info("WMS authentication successful")
            return access_token

        except requests.exceptions.RequestException as e:
            logger.error(f"WMS authentication failed: {e}")
            raise WMSAuthenticationError(f"Authentication request failed: {e}")
        except (KeyError, ValueError) as e:
            logger.error(f"Invalid authentication response: {e}")
            raise WMSAuthenticationError(f"Invalid response format: {e}")
    
    def make_authenticated_request(self, method: str, endpoint: str, headers: Optional[Dict] = None, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the WMS API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            headers: Additional headers to include
            **kwargs: Additional arguments for requests
            
        Returns:
            Response object
            
        Raises:
            WMSAPIError: If the request fails
        """
        token = self.authenticate()

        request_headers = headers or {}
        request_headers.update({
            'Authorization': token
        })

        if 'json' in kwargs or (method.upper() in ['POST', 'PUT', 'PATCH'] and 'data' in kwargs):
            request_headers.setdefault('Content-Type', 'application/json')
        
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=request_headers,
                timeout=60,
                **kwargs
            )
            # If unauthorized, invalidate token and retry once
            if response.status_code == 401:
                try:
                    cache.delete(self.token_cache_key)
                except Exception:
                    pass
                # Re-authenticate and retry once
                logger.warning(f"WMS unauthorized 401, retrying after token refresh | method={method} url={url}")
                token = self.authenticate()
                request_headers['Authorization'] = token
                response = self.session.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    timeout=60,
                    **kwargs
                )
            response.raise_for_status()
            return response

        except requests.exceptions.RequestException as e:
            logger.error(f"WMS API request failed: {method} {url} - {e}")
            status = None
            body = None
            if hasattr(e, 'response') and e.response is not None:
                try:
                    status = e.response.status_code
                except Exception:
                    status = None
                try:
                    body = e.response.text
                    logger.error(f"WMS API response body: {body}")
                except Exception:
                    body = None
            raise WMSAPIError(
                message=f"API request failed: {method} {url}",
                status_code=status,
                response_body=body,
                url=url,
            )

