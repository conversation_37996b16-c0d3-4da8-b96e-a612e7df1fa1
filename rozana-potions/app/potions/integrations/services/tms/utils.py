from typing import Dict, Any, List
from datetime import timedelta
from django.conf import settings
from core.repository.orders import order_repository
from core.repository.payments import payment_repository
from core.models import FacilityHubMapping
from potions.logging.utils import get_app_logger
from core.datetime_utils import get_current_time, format_datetime_to_string

logger = get_app_logger('tms_utils')


def build_forward_consignment_payload(order_reference: str, warehouse: str, wms_integration_name: str, order_update_payload: Dict[str, Any] = None) -> Dict[str, Any]:
    
    update_data = None
    if order_update_payload:
        update_data = order_update_payload.get('data', {})
        logger.info(f"Using order_update payload as primary source for order: {order_reference}")
    
    order_data = order_repository.get_order_with_address_and_items(order_reference)
    if not order_data:
        logger.error(f"Order not found in OMS database: {order_reference}. Cannot create forward consignment without order data.")
        return None

    order_mode = (order_data or {}).get('order_mode', '').lower()
    if not getattr(settings, 'TMS_FORWARD_CN_ALLOW_POS', False) and order_mode == 'pos':
        logger.info(f"Skipping forward CN for POS order: {order_reference}")
        return None

    # Always require order_data from OMS - cannot use items from payload without valid OMS order
    order_items = order_data.get('items', [])
    if not order_items:
        # Fallback to get items from order_items table if not included in main query
        order_items = order_repository.get_order_items_for_consignment(order_reference)
    
    logger.info(f"Using {len(order_items)} items from OMS database for order: {order_reference}")
    
    if not order_items:
        logger.warning(f"No items found for order: {order_reference}")
        return None

    is_cod_order = False
    cod_amount_from_payment = 0.0
    
    # Check payment mode from order_update payload first, then from OMS
    payment_mode = None
    if update_data:
        payment_mode = update_data.get('payment_mode', '').lower()
    
    # If no payment mode in payload, get from OMS order data
    if not payment_mode and order_data:
        # Try to get payment mode from order data or fetch from payment_details
        payment_details = payment_repository.get_payment_details_by_order_id(order_reference)
        if payment_details and len(payment_details) > 0:
            payment_mode = payment_details[0].get('payment_mode', '').lower()
    
    # Determine if this is a COD order and get COD amount
    if payment_mode == 'cod':
        is_cod_order = True
        # Get COD amount from payment_details table
        cod_amount = payment_repository.get_cod_amount_by_order_reference(order_reference)
        if cod_amount and cod_amount > 0:
            cod_amount_from_payment = float(cod_amount)
        else:
            # Fallback: calculate from order items if no payment record yet
            cod_amount_from_payment = 0.0
    
    logger.info(f"Payment mode: {payment_mode}, COD order: {is_cod_order}, COD amount: {cod_amount_from_payment}")

    # Hub mapping - prioritize warehouse_details from order_update
    if update_data and update_data.get('warehouse_details', {}).get('name'):
        facility_name = update_data['warehouse_details']['name']
        logger.info(f"Using facility_name from order_update: {facility_name}")
    else:
        facility_name = (order_data or {}).get('facility_name')
        logger.info(f"Using facility_name from OMS: {facility_name}")
    
    try:
        mapping = FacilityHubMapping.objects.select_related('facility', 'hub').get(
            facility__name=facility_name, is_active=True
        )
        hub_code = mapping.hub.hub_code
    except FacilityHubMapping.DoesNotExist:
        logger.error(f"No active hub mapping found for facility: {facility_name}")
        return None

    # Destination details - ALWAYS use OMS data for security and consistency
    # Order update payload should not override sensitive customer information
    pincode = order_data.get('postal_code', '')
    if pincode and len(pincode) == 5:
        pincode = '0' + pincode
    
    destination_details: Dict[str, Any] = {
        'address_line_1': order_data.get('address_line1', '') or '',
        'name': order_data.get('full_name', '') or order_data.get('customer_name', '') or '',
        'phone': order_data.get('phone_number', '') or order_data.get('phone', '') or '',
        'city': order_data.get('city', '') or '',
        'state': order_data.get('state', '') or '',
        'country': order_data.get('country', '') or 'India',
        'pincode': pincode or '',
    }
    if order_data.get('latitude') is not None:
        destination_details['latitude'] = float(order_data.get('latitude'))
    if order_data.get('longitude') is not None:
        destination_details['longitude'] = float(order_data.get('longitude'))
    
    # Validate that we have essential customer information from OMS
    if not destination_details['name'] or not destination_details['address_line_1']:
        logger.error(f"Missing essential customer information from OMS for order: {order_reference}")
        return None
    
    logger.info(f"Using destination_details from OMS database for order: {order_reference}")

    # Build pieces_detail
    pieces_detail_list: List[Dict[str, Any]] = []
    total_cod_amount = 0.0

    for item in order_items:
        # For order_update payload, prioritize invoice_qty over order_quantity
        if update_data:
            qty = 0
            invoice_details = item.get('invoice_details', [])
            if invoice_details and isinstance(invoice_details, list) and invoice_details[0].get('invoice_qty') is not None:
                qty = int(invoice_details[0].get('invoice_qty', 0))
            else:
                qty = int(item.get('order_quantity', 0) or 0)
            
            piece_detail = {
                'description': item.get('sku_description', ''),
                'piece_product_code': item.get('sku_code', ''),
                'quantity': qty,
            }
        else:
            # OMS fallback
            qty = int(item.get('quantity', 0) or 0)
            piece_detail = {
                'description': item.get('product_name', ''),
                'piece_product_code': item.get('sku_code', ''),
                'quantity': qty,
            }
        
        if is_cod_order:
            if update_data:
                item_cod_amount = float(item.get('unit_price', 0) or 0) * qty
            else:
                item_cod_amount = float(item.get('sale_price', 0) or 0) * qty
            piece_detail['cod_amount'] = item_cod_amount
            total_cod_amount += item_cod_amount
        pieces_detail_list.append(piece_detail)

    payload: Dict[str, Any] = {
        'service_type_id': 'PREMIUM',
        'action_type': 'delivery',
        'consignment_type': 'forward',
        'movement_type': 'forward',
        'verify_otp_on_delivery': False,
        'currency': 'INR',
        'reference_number': f'INV{order_reference}',
        'customer_reference_number': order_reference,
        'hub_code': hub_code,
        'destination_details': destination_details,
        'pieces_detail_list': pieces_detail_list,
    }

    if is_cod_order:
        payload['cod_amount'] = cod_amount_from_payment if cod_amount_from_payment > 0 else total_cod_amount
        logger.info(f"COD order detected - adding cod_amount: {payload['cod_amount']} to TMS payload")
    else:
        logger.info(f"Non-COD order - no cod_amount added to TMS payload")

    current_time = get_current_time()
    allocation_time = current_time.replace(minute=0, second=0, microsecond=0)
    if current_time.minute > 0 or current_time.second > 0 or current_time.microsecond > 0:
        allocation_time = allocation_time + timedelta(hours=1)
    payload['allocation_time'] = format_datetime_to_string(allocation_time)

    # Add delivery_time_slot_start (current time when creating consignment)
    payload['delivery_time_slot_start'] = format_datetime_to_string(current_time)

    # Add mandatory delivery_time_slot_end (ETA from orders table or allocation_time as fallback)
    if order_data and order_data.get('eta'):
        try:
            payload['delivery_time_slot_end'] = format_datetime_to_string(order_data.get('eta'))
        except Exception:
            payload['delivery_time_slot_end'] = format_datetime_to_string(allocation_time)
    else:
        payload['delivery_time_slot_end'] = format_datetime_to_string(allocation_time)

    logger.info(f"Forward consignment payload built for order: {order_reference}")
    return payload
