from django.urls import path
from integrations.views.consignments import (
    ForwardConsignmentAPIView,
    ReverseConsignmentAPIView,
    ReverseConsignmentByReferenceAPIView,
    ConsignmentTrackingAPIView
)
from integrations.views.webhooks import TMSConsignmentWebhookView
from integrations.views.invoice_callback import (
    InvoiceCallbackAPIView,
    InvoiceStatusAPIView
)
from integrations.views.orders import (
    WMSOrderCreateAPIView,
    WMSOrderCancelAPIView,
)
from integrations.views.order_update import OrderUpdateAPIView
from integrations.views.order_update_trigger import OrderUpdateTriggerAPIView
from integrations.views.sale_return import SalesReturnView
from integrations.views.grn_callback import GRNCallbackAPIView
from integrations.views.health import (
    HealthCheckAPIView
)

from integrations.webhooks.wms.cancel_order import WMSOrderCancelWebhookView

app_name = 'integrations'

urlpatterns = [

    # Health check endpoint
    path('health/', HealthCheckAPIView.as_view(), name='health_check'),

    # WMS Orchestrator API endpoints
    path('order/create/', WMSOrderCreateAPIView.as_view(), name='wms_order_create'),
    path('order/cancel/', WMSOrderCancelAPIView.as_view(), name='wms_order_cancel'),
    path('sales-return/', SalesReturnView.as_view(), name='sales_return'),

    # Orchestrator API endpoints
    path('consignment/create/', ForwardConsignmentAPIView.as_view(), name='create_forward_consignment'),
    path('consignment/create_reverse/', ReverseConsignmentAPIView.as_view(), name='create_reverse_consignment'),
    path('consignment/create_reverse/return_reference/', ReverseConsignmentByReferenceAPIView.as_view(), name='create_reverse_consignment_by_reference'),
    path('consignment/track/', ConsignmentTrackingAPIView.as_view(), name='track_consignment'),

    # TMS Webhook endpoints
    path('webhooks/tms/consignment/', TMSConsignmentWebhookView.as_view(), name='tms_consignment_webhook'),

    # WMS Webhook endpoints
    path('webhooks/wms/order/invoice/<str:wms_integration_name>/', InvoiceCallbackAPIView.as_view(), name='invoice_callback'),
    path('webhooks/wms/order/cancel/', WMSOrderCancelWebhookView.as_view(), name='wms_order_cancellation_webhook'),
    path('webhooks/wms/order/grn/<str:wms_integration_name>/', GRNCallbackAPIView.as_view(), name='grn_callback'),
    path('webhooks/wms/order_update/<str:wms_integration_name>/', OrderUpdateAPIView.as_view(), name='order_update'),

    # Invoice callback endpoints with WMS integration name
    path('invoice/callback/<str:wms_integration_name>/', InvoiceCallbackAPIView.as_view(), name='invoice_callback'),
    path('invoice/status/<str:task_id>/', InvoiceStatusAPIView.as_view(), name='invoice_status'),
]