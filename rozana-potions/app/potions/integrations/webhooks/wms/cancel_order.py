"""
WMS Order Cancellation Webhook API

Simple webhook endpoint to receive WMS order cancellation data.
"""

import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView

# Repository
from core.repository.orders import OrderRepository

#Constants
from payments.constants import OrderStatus

# Events
from integrations.tasks.order_refunds import process_cancelled_quantity_refunds_task

from potions.logging.utils import get_app_logger
logger = get_app_logger('wms_order_cancel')


@method_decorator(csrf_exempt, name='dispatch')
class WMSOrderCancelWebhookView(APIView):
    """
    Simple webhook endpoint to receive WMS order cancellation updates.
    """
    
    def _validate_payload(self, payload):
        """Simple validation for required fields only"""
        errors = []
        
        if not payload.get('warehouse'):
            errors.append("Missing warehouse")
            
        data = payload.get('data', {})
        if not data.get('order_reference'):
            errors.append("Missing order_reference")
        if not data.get('order_status'):
            errors.append("Missing order_status")
            
        items = data.get('items', [])
        if not items:
            errors.append("Missing items")
        else:
            for i, item in enumerate(items):
                if not item.get('sku_code'):
                    errors.append(f"Item {i}: missing sku_code")
                if not item.get('status'):
                    errors.append(f"Item {i}: missing status")
                if 'cancelled_quantity' not in item:
                    errors.append(f"Item {i}: missing cancelled_quantity")
                if 'pack_uom_quantity' not in item:
                    errors.append(f"Item {i}: missing pack_uom_quantity")
        
        return errors
    
    def _extract_data(self, payload):
        """Extract only required fields"""
        data = payload.get('data', {})
        
        extracted = {
            # Header level
            'warehouse': payload.get('warehouse', '').strip(),
            'order_status': data.get('order_status', '').strip(), 
            'order_reference': data.get('order_reference', '').strip(),
            # Line level
            'items': []
        }
        
        for item in data.get('items', []):
            extracted['items'].append({
                'status': item.get('status', '').strip(),
                'sku_code': item.get('sku_code', '').strip(),
                'pack_uom_quantity': float(item.get('pack_uom_quantity', 0)),
                'cancelled_quantity': float(item.get('cancelled_quantity', 0))
            })
            
        return extracted
    
    def post(self, request):
        """
        Handle incoming WMS webhook for order cancellation updates.
        
        Expected payload structure:
        {
            "data": {
                "items": [...],
                "order_reference": "ZTEU634",
                "customer_id": 100,
                "order_status": "cancelled",
                ...
            },
            "warehouse": "ROZANA_TEST_WH1"
        }
        """
        try:
            # Parse the webhook payload
            try:
                payload = request.data if hasattr(request, 'data') else json.loads(request.body)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in webhook payload: {str(e)}")
                return JsonResponse({'success': False, 'error': 'Invalid JSON payload'}, status=400)

            # Validate payload
            errors = self._validate_payload(payload)
            if errors:
                logger.error(f"Validation errors: {errors}")
                return JsonResponse({'success': False, 'error': 'Validation errors', 'details': errors}, status=400)

            # Extract data
            data = self._extract_data(payload)
            order_reference = data['order_reference']
            items = data['items']
            warehouse = data['warehouse']
            order_status = data['order_status']

            # Log the received webhook
            logger.info(f"WMS cancellation webhook received - Order: {order_reference}, Items: {len(items)}, Warehouse: {warehouse}")
            logger.info(f"Full payload: {json.dumps(payload, indent=2)}")

            # Check if order exists first
            order_repository = OrderRepository()
            order_exists = order_repository.get_order_by_id(order_reference)
            if not order_exists:
                logger.error(f"Order not found: {order_reference}")
                return JsonResponse({
                    'success': False, 
                    'error': 'Order not found',
                    'order_reference': order_reference,
                    'warehouse': warehouse
                }, status=404)

            # Process each item
            processed_items = []
            failed_items = []
            success_count = 0
            
            for item in items:
                try:
                    # Find order item using repository
                    order_item = order_repository.get_order_item_by_wh_sku_and_pack_qty(
                        order_id=order_reference,
                        sku_code=item['sku_code'],
                        pack_uom_quantity=item['pack_uom_quantity']
                    )
                    
                    if order_item:
                        # Map item status to numeric status (adjust based on your system)
                        numeric_status = None
                        if item['status'] == 'cancelled':
                            numeric_status = OrderStatus.WMS_CANCELED
                        
                        # Update order item using repository
                        updated = order_repository.update_order_item_cancelled_quantity(
                            item_id=order_item['id'],
                            cancelled_quantity=float(item['cancelled_quantity']),
                            new_status=numeric_status
                        )

                        if updated:
                            processed_items.append({
                                'sku_code': item['sku_code'],
                                'pack_uom_quantity': item['pack_uom_quantity'],
                                'cancelled_quantity': item['cancelled_quantity'],
                                'status': item['status'],
                                'updated': True
                            })
                            success_count += 1
                            logger.info(f"Successfully updated item: {item['sku_code']}")
                        else:
                            failed_items.append({
                                'sku_code': item['sku_code'],
                                'error': 'Failed to update item'
                            })
                            logger.error(f"Failed to update item: {item['sku_code']}")
                    else:
                        failed_items.append({
                            'sku_code': item['sku_code'],
                            'error': 'Order item not found'
                        })
                        logger.warning(f"Order item not found: SKU={item['sku_code']}, Pack UOM Qty={item['pack_uom_quantity']}")
                        
                except Exception as item_error:
                    logger.error(f"Error processing item: {str(item_error)}", exc_info=True)
                    failed_items.append({
                        'sku_code': item.get('sku_code', 'unknown'),
                        'error': str(item_error)
                    })
            
            # Update order status if provided
            order_status_updated = False
            if order_status:
                try:
                    # Map order status to numeric status (adjust based on your system)
                    numeric_order_status = None
                    if order_status == 'cancelled':
                        numeric_order_status = OrderStatus.WMS_CANCELED

                    if numeric_order_status:
                        order_status_updated = order_repository.update_order_status(
                            order_id=order_reference,
                            new_status=numeric_order_status
                        )

                        if order_status_updated:
                            logger.info(f"Successfully updated order status to: {order_status}")
                        else:
                            logger.error(f"Failed to update order status")

                except Exception as status_error:
                    logger.error(f"Error updating order status: {str(status_error)}", exc_info=True)

            # Determine overall success
            total_items = len(items)
            has_failures = len(failed_items) > 0
            overall_success = success_count > 0 and not has_failures

            # Process cancelled quantity refunds
            process_cancelled_quantity_refunds_task.apply_async(args=[order_reference, warehouse])
 
            response_data = {
                'success': overall_success,
                'message': 'Webhook processed successfully' if overall_success else 'Webhook processed with errors',
                'order_reference': order_reference,
                'items_count': total_items,
                'processed_items': processed_items,
                'failed_items': failed_items,
                'success_count': success_count,
                'failed_count': len(failed_items),
                'order_status_updated': order_status_updated,
                'warehouse': warehouse
            }
 
            status_code = 200 if overall_success else 422
            return JsonResponse(response_data, status=status_code)

        except Exception as e:
            logger.error(f"Error processing webhook: {str(e)}", exc_info=True)
            return JsonResponse({
                'success': False,
                'error': 'Internal server error'
            }, status=500)