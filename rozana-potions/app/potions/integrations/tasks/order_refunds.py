from django.conf import settings
from potions.celery import app as celery_app
from payments.refunds.refund_processor import RefundProcessor

from potions.logging.utils import get_app_logger
logger = get_app_logger('refund_tasks')


@celery_app.task
def process_cancelled_and_unfulfilled_quantity_refunds_task(order_id: str, facility_name: str):
    """
    Celery task to process refunds for cancelled and unfulfilled quantities.
    Args:
        order_id (str): The order ID to process unfulfilled refunds for
    Returns:
        dict: Task execution result with status and details
    """
    try:
        logger.info(f"[REFUND_TASK] Starting cancelled and unfulfilled quantity refund processing for order: {order_id}")

        # Initialize refund processor
        refund_processor = RefundProcessor()
        # Process cancelled quantity refunds
        cancelled_refund_result = refund_processor.create_refund_for_cancelled_quantity(order_id)
        # Process unfulfilled quantity refunds
        unfulfilled_refund_result = refund_processor.create_refund_for_unfulfilled_quantity(order_id)

        if cancelled_refund_result.success and unfulfilled_refund_result.success:
            logger.info(f"[REFUND_TASK] Cancelled and unfulfilled quantity refunds processed successfully for order {order_id}")
            return {
                'status': 'success',
                'order_id': order_id,
                'cancelled_refund_message': cancelled_refund_result.message,
                'cancelled_refund_details': cancelled_refund_result.data,
                'unfulfilled_refund_message': unfulfilled_refund_result.message,
                'unfulfilled_refund_details': unfulfilled_refund_result.data
            }
        else:
            logger.error(f"[REFUND_TASK] Cancelled and unfulfilled quantity refund processing failed for order {order_id}: {refund_result.message}")
            return {
                'status': 'failed',
                'order_id': order_id,
                'cancelled_refund_message': cancelled_refund_result.message,
                'cancelled_refund_details': cancelled_refund_result.data,
                'unfulfilled_refund_message': unfulfilled_refund_result.message,
                'unfulfilled_refund_details': unfulfilled_refund_result.data
            }

    except Exception as exc:
        logger.error(f"[REFUND_TASK] Cancelled and unfulfilled quantity refund task failed for order {order_id}: {str(exc)}", exc_info=True)
        return {
            'status': 'failed',
            'order_id': order_id,
            'message': f'Cancelled and unfulfilled quantity refund task failed: {str(exc)}',
            'error': str(exc)
        }


@celery_app.task
def process_cancelled_quantity_refunds_task(order_id: str, facility_name: str):
    """
    Celery task to process refunds for cancelled quantities.
    Args:
        order_id (str): The order ID to process cancelled refunds for
    Returns:
        dict: Task execution result with status and details
    """
    try:
        logger.info(f"[REFUND_TASK] Starting cancelled quantity refund processing for order: {order_id}")

        # Initialize refund processor
        refund_processor = RefundProcessor()
        # Process cancelled quantity refunds
        refund_result = refund_processor.create_refund_for_cancelled_quantity(order_id)

        if refund_result.success:
            logger.info(f"[REFUND_TASK] Cancelled quantity refunds processed successfully for order {order_id}")
            return {
                'status': 'success',  
                'order_id': order_id,
                'message': refund_result.message,
                'refund_details': refund_result.data
            }
        else:
            logger.error(f"[REFUND_TASK] Cancelled quantity refund processing failed for order {order_id}: {refund_result.message}")
            return {
                'status': 'failed',
                'order_id': order_id,
                'message': refund_result.message,
                'refund_details': refund_result.data
            }

    except Exception as exc:
        logger.error(f"[REFUND_TASK] Cancelled quantity refund task failed for order {order_id}: {str(exc)}", exc_info=True)
        return {
            'status': 'failed',
            'order_id': order_id,
            'message': f'Cancelled quantity refund task failed: {str(exc)}',
            'error': str(exc)
        }

