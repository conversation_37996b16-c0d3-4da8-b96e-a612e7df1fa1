from django.conf import settings
from integrations.services.tms.consignment_service import ForwardCNService
from integrations.services.tms.utils import build_forward_consignment_payload
from potions.celery import app as celery_app

from potions.logging.utils import get_app_logger
logger = get_app_logger('forward_consignment')


@celery_app.task(bind=True, max_retries=3)
def create_forward_consignment_task(self, order_reference, warehouse, invoice_date_str=None, wms_integration_name='default', order_update_payload=None):
    try:
        logger.info(f"Starting forward consignment creation for order: {order_reference}")
        
        if not getattr(settings, 'TMS_FORWARD_CN_ENABLED', True):
            logger.info(f"Forward consignment disabled for order {order_reference}")
            return {
                'status': 'skipped',
                'order_reference': order_reference,
                'message': 'Forward consignment creation disabled'
            }
        
        payload = build_forward_consignment_payload(order_reference, warehouse, wms_integration_name, order_update_payload)
        
        if not payload:
            logger.error(f"Could not build forward consignment payload for order {order_reference} - order not found in OMS or validation failed")
            raise Exception(f"Forward consignment creation failed: Order {order_reference} not found in OMS database or validation failed")
        
        if 'pieces_detail' in payload and 'pieces_detail_list' not in payload:
            payload['pieces_detail_list'] = payload.pop('pieces_detail')

        forward_service = ForwardCNService()
        tms_response = forward_service.create(**payload)
        
        logger.info(f"Forward consignment created successfully for order {order_reference} tms_response is {tms_response}")
        
        return {
            'status': 'success',
            'order_reference': order_reference,
            'warehouse': warehouse,
            'tms_response': tms_response
        }
        
    except Exception as exc:
        logger.error(f"Forward consignment creation failed for order {order_reference}: {str(exc)}")
        if isinstance(exc, TypeError):
            raise
        
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying forward consignment task. Attempt {self.request.retries + 1}/{self.max_retries}")
            raise self.retry(countdown=60 * (self.request.retries + 1), exc=exc)
        
        raise
