FROM python:3.11-slim AS base

ENV PYTHONUNBUFFERED=1

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Copy entrypoint script (only if you need migrations or setup before Gun<PERSON>)
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000

# Use entrypoint if you need migrations/startup scripts
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command starts Gunicorn
CMD ["gunicorn", "-w", "4", "auth_service.wsgi:application", "--bind", "0.0.0.0:8000"]
