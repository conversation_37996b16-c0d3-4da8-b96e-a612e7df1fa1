## 1️⃣ Environment Setup

Create a `.env` file in your project root and add:

# Firebase
FIREBASE_SERVICE_ACCOUNT_KEY_PATH=/absolute/path/to/firebase-key.json

# PostgreSQL Database
DB_NAME=mydb
DB_USER=myuser
DB_PASSWORD=mypassword
DB_HOST=localhost
DB_PORT=5432

## 2️⃣ Installation

Install dependencies:
pip install -r requirements.txt


## 3️⃣ API Endpoints

### 🔍 Check if a Number Exists
**Method:** GET  
**Path:** `/api/check-number/`

#### Option 1 – Pass Token in Query Parameters
curl "http://domain/api/check-number/?number=+**********&token=YOUR_TOKEN"


#### Option 2 – Pass Token in Headers (Recommended)
curl -H "Authorization: Bearer YOUR_TOKEN"
"http://domain/api/check-number/?number=+**********"


**Example Response:**
{
"number_exists": true
}

### 🔑 Generate OAuth2 Token
**Method:** POST  
**Path:** `/o/token/`

curl -X POST "http://domain/o/token/"
-d "grant_type=client_credentials"
-d "client_id=YOUR_CLIENT_ID"
-d "client_secret=YOUR_CLIENT_SECRET"


### ✅ Validate OAuth2 Token
**Method:** GET  
**Path:** `/api/check-token/`

curl -X GET "http://domain/api/check-token/?token=YOUR_TOKEN"


## 4️⃣ Notes
- Your Firebase service account key must have Firestore read access.
- You can pass the OAuth2 token either in query parameters or HTTP headers — headers are preferred for security.
- PostgreSQL credentials must match your `.env` file configuration.
- If duplicate query parameters are provided (`number` or `token`), Django will use the last value.

---

## 5️⃣ Example Local Testing

Check number (local)
curl -H "Authorization: Bearer my_token"
"http://127.0.0.1:8000/api/check-number/?number=+************"

curl -X GET "http://127.0.0.1:8000/api/check-token/?token=my_token"
