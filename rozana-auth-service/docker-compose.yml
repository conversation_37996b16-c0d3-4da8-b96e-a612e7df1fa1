version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: auth_service_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: auth_service_db
      POSTGRES_USER: auth_user
      POSTGRES_PASSWORD: auth_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5431:5432"
    networks:
      - auth_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U auth_user -d auth_service_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  auth_service:
    build: .
    container_name: auth_service_app
    env_file:
      - .env
    restart: unless-stopped
    environment:
      DB_NAME: auth_service_db
      DB_USER: auth_user
      DB_PASSWORD: auth_password
      DB_HOST: postgres
      DB_PORT: 5432
      DEBUG: "True"
      DJANGO_SETTINGS_MODULE: auth_service.settings
    ports:
      - "8021:8000"  # <-- THIS IS THE FIX
    networks:
      - auth_network
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - .:/app
    command: >
      sh -c "python manage.py migrate &&
             python manage.py runserver 0.0.0.0:8000"
    stdin_open: true
    tty: true

volumes:
  postgres_data:

networks:
  auth_network:
    driver: bridge
