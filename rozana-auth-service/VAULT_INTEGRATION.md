# Vault Secrets Integration

This project integrates with HashiCorp Vault to securely manage environment variables and secrets during the build and deployment process.

## Overview

The integration retrieves secrets from <PERSON>ault and creates an `.env` file that is used during the Docker build process. This ensures sensitive configuration data is never hardcoded in the repository.

## Environment Variables Required

### GitHub Repository Variables
Set these in your GitHub repository settings under **Settings > Secrets and variables > Actions > Variables**:

- `VAULT_ADDR`: The URL of your Vault server (e.g., `https://vault.example.com`)
- `VAULT_SECRETS_MOUNT`: The mount point for your KV secrets engine (e.g., `secret`)
- `VAULT_SECRETS_PATH`: The path to your secrets in Vault (e.g., `myapp/production`)
- `VAULT_SECRETS_KEY`: The key name containing your environment file content (e.g., `env_vars`)

### GitHub Repository Secrets
Set this in your GitHub repository settings under **Settings > Secrets and variables > Actions > Secrets**:

- `VAULT_TOKEN`: Your Vault authentication token with read access to the specified path

## Vault Secret Structure

The integration supports two approaches for storing secrets in Vault:

### **Approach 1: Direct Key-Value Storage (Recommended)**
Store environment variables directly as key-value pairs in Vault:

```bash
# Store individual environment variables as separate fields
vault kv put secret/myapp/production \
  DATABASE_URL="********************************/db" \
  REDIS_URL="redis://redis-host:6379/0" \
  SECRET_KEY="your-secret-key" \
  DEBUG="false"
```

### **Approach 2: Single Field with Environment File Content**
Store all environment variables as a single field containing the entire .env file content:

```bash
# Store as a single field containing env file content
vault kv put secret/myapp/production env_vars=@production.env
```

Where `production.env` contains your environment variables:
```env
DATABASE_URL=********************************/db
REDIS_URL=redis://redis-host:6379/0
SECRET_KEY=your-secret-key
DEBUG=false
```

**Note**: The integration will automatically try the specific field approach first (if `VAULT_SECRETS_KEY` is provided), then fall back to extracting all key-value pairs from the secret if the field is not found or empty.

## Workflow Process

The GitHub Actions workflow performs these steps:

1. **Checkout code**: Gets the repository code
2. **Install Vault CLI**: Installs HashiCorp Vault CLI
3. **Retrieve secrets**: Connects to Vault and downloads the environment file
4. **Build Docker image**: Uses the retrieved `.env` file during build
5. **Push to ECR**: Deploys the built image

## Local Development

For local development, use the provided helper script:

```bash
# Set required environment variables
export VAULT_ADDR=https://your-vault-server.com
export VAULT_TOKEN=your-vault-token
export VAULT_SECRETS_MOUNT=secret
export VAULT_SECRETS_PATH=myapp/development
export VAULT_SECRETS_KEY=env_vars

# Run the helper script
./scripts/vault-secrets.sh
```

This will create a `.env` file in your project root with the secrets from Vault.

## Docker Integration

The Dockerfile has been updated to:
- Accept an `ENV_FILE` build argument
- Copy the environment file during build
- Make environment variables available to the application

## Security Best Practices

1. **Never commit `.env` files** to version control
2. **Use least-privilege tokens** for Vault access
3. **Rotate Vault tokens regularly**
4. **Monitor Vault access logs**
5. **Use different Vault paths** for different environments

## Troubleshooting

### Common Issues

1. **Vault authentication fails**
   - Check `VAULT_ADDR` is correct and accessible
   - Verify `VAULT_TOKEN` has proper permissions
   - Ensure token hasn't expired

2. **Secret not found**
   - Verify `VAULT_SECRETS_MOUNT` exists
   - Check `VAULT_SECRETS_PATH` is correct
   - Confirm `VAULT_SECRETS_KEY` matches the field name

3. **Build fails with missing environment**
   - Ensure the secret contains valid environment variables
   - Check the format is `KEY=value` pairs

### Debug Commands

```bash
# Test Vault connection
vault auth -method=token token="$VAULT_TOKEN"

# List available mounts
vault secrets list

# Check if secret exists
vault kv get secret/myapp/production

# View secret metadata
vault kv metadata secret/myapp/production
```

## Example Vault Commands

```bash
# Create a secret with environment variables
vault kv put secret/myapp/production env_vars=@production.env

# Update a specific field
vault kv patch secret/myapp/production env_vars=@updated.env

# Read the secret
vault kv get -field=env_vars secret/myapp/production
```

## GitHub Actions Setup Checklist

- [ ] Set `VAULT_ADDR` in repository variables
- [ ] Set `VAULT_SECRETS_MOUNT` in repository variables  
- [ ] Set `VAULT_SECRETS_PATH` in repository variables
- [ ] Set `VAULT_SECRETS_KEY` in repository variables
- [ ] Set `VAULT_TOKEN` in repository secrets
- [ ] Verify Vault token has read permissions
- [ ] Test the workflow with a tag push
- [ ] Confirm `.env` file is created during build
- [ ] Validate application starts with retrieved secrets
