"""
URL configuration for auth_service project.
"""
from django.contrib import admin
from django.urls import path, include
from .views import CheckTokenView, CheckNumberView, UserCreateView

urlpatterns = [
    # Admin and OAuth
    path('admin/', admin.site.urls),
    path('o/', include('oauth2_provider.urls', namespace='oauth2_provider')),

    # API endpoints
    path('api/check-token/', CheckTokenView.as_view(), name='check_token'),
    path('api/check-number/', CheckNumberView.as_view(), name='check_number'),
    # path('api/user/search/', UserSearchView.as_view(), name='user_search'),
    path('api/customer', UserCreateView.as_view(), name='user_create'),
]