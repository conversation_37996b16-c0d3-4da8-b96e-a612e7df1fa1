# Rozana OMS API - Complete CURL Examples

This document provides comprehensive CURL examples for all available endpoints in the Rozana Order Management System API.

## Base Configuration

```bash
# Set base URL (adjust as needed)
BASE_URL="http://localhost:8000"
# or for production: BASE_URL="https://api.rozana.tech"

# Authentication tokens (get these from auth endpoints)
APP_TOKEN="your_firebase_app_token"
POS_TOKEN="your_firebase_pos_token"  
API_TOKEN="your_api_token"
```

## Health Check

### Health Status
```bash
curl -X GET "${BASE_URL}/health"
```

## Authentication Endpoints

### Firebase App Token
```bash
curl -X POST "https://securetoken.googleapis.com/v1/token?key=AIzaSyBEEWROSUbSLVl9T3HHSp8VdgsU6oTrdFI" \
  -H "Content-Type: application/json" \
  -d '{
    "grant_type": "refresh_token",
    "refresh_token": "your_refresh_token_here"
  }'
```

### Firebase POS Token
```bash
curl -X POST "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyC_fE1A0JapukDalgCdJix4mmoVWx-K-2g" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password",
    "returnSecureToken": true
  }'
```

### API Token (OAuth)
```bash
curl -X POST "http://localhost:8021/o/token/" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=client_credentials&client_id=your_client_id&client_secret=your_client_secret"
```

## APP APIs (Firebase App Token Required)

### Get All Orders
```bash
curl -X GET "${BASE_URL}/app/v1/orders?page=1&sort_order=desc&page_size=20" \
  -H "Authorization: ${APP_TOKEN}"
```

### Get All Orders with Search
```bash
curl -X GET "${BASE_URL}/app/v1/orders?page=1&sort_order=desc&search=ORD123" \
  -H "Authorization: ${APP_TOKEN}"
```

### Get All Orders Excluding Status
```bash
curl -X GET "${BASE_URL}/app/v1/orders?page=1&sort_order=desc&exclude_status=10" \
  -H "Authorization: ${APP_TOKEN}"
```

### Get Order Details
```bash
curl -X GET "${BASE_URL}/app/v1/order_details?order_id=ORD123" \
  -H "Authorization: ${APP_TOKEN}"
```

### Order Again Products
```bash
curl -X GET "${BASE_URL}/app/v1/order_again?page_size=20&page=1" \
  -H "Authorization: ${APP_TOKEN}"
```

### Create Order
```bash
curl -X POST "${BASE_URL}/app/v1/create_order" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "customer_name": "John Doe",
    "facility_id": "FAC-001",
    "facility_name": "ROZANA_TEST_WH1",
    "eta": "2025-09-08T21:00:00",
    "total_amount": 299.99,
    "payment": [{
      "payment_mode": "cod",
      "create_payment_order": false,
      "amount": 299.99
    }],
    "items": [{
      "sku": "ROZ5588-1PCS",
      "quantity": 1.0,
      "unit_price": 299.99,
      "sale_price": 299.99
    }],
    "address": {
      "full_name": "John Doe",
      "phone_number": "9110345323",
      "address_line1": "Test Address Line 1",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postal_code": "400001",
      "country": "india",
      "type_of_address": "home",
      "longitude": 72.8777,
      "latitude": 19.076
    }
  }'
```

### Update Order Status
```bash
curl -X PUT "${BASE_URL}/app/v1/update_order_status" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "status": "confirmed"
  }'
```

### Update Item Status
```bash
curl -X PUT "${BASE_URL}/app/v1/update_item_status" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "sku": "ROZ5588-1PCS",
    "status": "confirmed"
  }'
```

### Cancel Order
```bash
curl -X POST "${BASE_URL}/app/v1/cancel_order" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123"
  }'
```

### Encrypt Customer Code
```bash
curl -X POST "${BASE_URL}/app/v1/encrypt_customer_code" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_code": "CUST123"
  }'
```

### Create Return
```bash
curl -X POST "${BASE_URL}/app/v1/create_return" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "return_reason": "Defective product",
    "order_full_return": false,
    "items": [{
      "sku": "ROZ1602-1PCS",
      "quantity": 1
    }]
  }'
```

### Get Return Reasons
```bash
curl -X GET "${BASE_URL}/app/v1/returns/reasons" \
  -H "Authorization: ${APP_TOKEN}"
```

## Payment APIs (App Level)

### Create Payment Order
```bash
curl -X POST "${BASE_URL}/app/v1/create_payment_order" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "amount": 299.99,
    "customer_id": "customer_123",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "+91-9876543210",
    "notes": {
      "description": "Payment for order ORD123"
    }
  }'
```

### Verify Payment
```bash
curl -X POST "${BASE_URL}/app/v1/verify_payment" \
  -H "Authorization: ${APP_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "oms_order_id": "ORD123",
    "razorpay_order_id": "order_sample123",
    "razorpay_payment_id": "pay_sample123",
    "razorpay_signature": "sample_signature_hash"
  }'
```

### Get Payment Status
```bash
curl -X GET "${BASE_URL}/app/v1/payment_status/ORD123" \
  -H "Authorization: ${APP_TOKEN}"
```

## POS APIs (Firebase POS Token Required)

### Get All Orders (POS)
```bash
curl -X GET "${BASE_URL}/pos/v1/orders?facility_name=ROZANA_TEST_WH1&page=1&sort_order=desc" \
  -H "Authorization: ${POS_TOKEN}"
```

### Get All Orders with Search (POS)
```bash
curl -X GET "${BASE_URL}/pos/v1/orders?facility_name=ROZANA_TEST_WH1&page=1&sort_order=desc&search=ORD123" \
  -H "Authorization: ${POS_TOKEN}"
```

### Get Order Details (POS)
```bash
curl -X GET "${BASE_URL}/pos/v1/order_details?order_id=ORD123&facility_name=ROZANA_TEST_WH1" \
  -H "Authorization: ${POS_TOKEN}"
```

### Create Order (POS)
```bash
curl -X POST "${BASE_URL}/pos/v1/create_order" \
  -H "Authorization: ${POS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "customer_name": "John POS Test",
    "facility_id": "FAC-001",
    "facility_name": "ROZANA_TEST_WH1",
    "total_amount": 87,
    "payment": [{
      "payment_mode": "cash",
      "create_payment_order": false,
      "amount": 87
    }],
    "items": [{
      "sku": "ROZ16863-1PCS",
      "quantity": 1,
      "unit_price": 39,
      "sale_price": 39
    }],
    "address": {
      "full_name": "John POS Test",
      "phone_number": "+91-9876543210",
      "address_line1": "POS Test Address",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postal_code": "400001",
      "country": "india",
      "type_of_address": "home",
      "longitude": 72.8777,
      "latitude": 19.076
    }
  }'
```

### Update Order Status (POS)
```bash
curl -X PUT "${BASE_URL}/pos/v1/update_order_status" \
  -H "Authorization: ${POS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "status": "confirmed"
  }'
```

### Update Item Status (POS)
```bash
curl -X PUT "${BASE_URL}/pos/v1/update_item_status" \
  -H "Authorization: ${POS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "sku": "ROZ5588-1PCS",
    "status": "confirmed"
  }'
```

### Create Return (POS)
```bash
curl -X POST "${BASE_URL}/pos/v1/create_return" \
  -H "Authorization: ${POS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "return_reason": "Defective product",
    "order_full_return": false,
    "items": [{
      "sku": "ROZ1602-1PCS",
      "quantity": 1
    }]
  }'
```

## API Token Validation Endpoints

### Get Orders (Token Validation)
```bash
curl -X GET "${BASE_URL}/api/v1/get_orders?customer_id=customer_123&page_size=20&page=1" \
  -H "Authorization: ${API_TOKEN}"
```

### Get Orders with Search (Token Validation)
```bash
curl -X GET "${BASE_URL}/api/v1/get_orders?customer_id=customer_123&page_size=20&page=1&search=ORD123" \
  -H "Authorization: ${API_TOKEN}"
```

### Get Orders by Phone Number
```bash
curl -X GET "${BASE_URL}/api/v1/get_orders_by_phone_number?phone_number=9123456789&page_size=20&page=1&sort_order=desc" \
  -H "Authorization: ${API_TOKEN}"
```

### Get Order Details (Token Validation)
```bash
curl -X GET "${BASE_URL}/api/v1/order_details?order_id=ORD123" \
  -H "Authorization: ${API_TOKEN}"
```

### Get Order Items
```bash
curl -X GET "${BASE_URL}/api/v1/order_items?customer_id=customer_123&order_id=ORD123" \
  -H "Authorization: ${API_TOKEN}"
```

### Cancel Order (Token Validation)
```bash
curl -X POST "${BASE_URL}/api/v1/cancel_order" \
  -H "Authorization: ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "order_id": "ORD123"
  }'
```

### Create Order (Token Validation)
```bash
curl -X POST "${BASE_URL}/api/v1/create_order" \
  -H "Authorization: ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "customer_name": "John API Test",
    "facility_id": "FAC-001",
    "facility_name": "ROZANA_TEST_WH1",
    "total_amount": 299.99,
    "payment": [{
      "payment_mode": "cod",
      "create_payment_order": false,
      "amount": 299.99
    }],
    "items": [{
      "sku": "ROZ5588-1PCS",
      "quantity": 1.0,
      "unit_price": 299.99,
      "sale_price": 299.99
    }],
    "address": {
      "full_name": "John API Test",
      "phone_number": "9110345323",
      "address_line1": "API Test Address",
      "city": "Mumbai",
      "state": "Maharashtra",
      "postal_code": "400001",
      "country": "india",
      "type_of_address": "home",
      "longitude": 72.8777,
      "latitude": 19.076
    }
  }'
```

## Return APIs (Token Validation)

### Return Items
```bash
curl -X POST "${BASE_URL}/api/v1/return_items" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "order_id": "ORD123",
    "items": [{
      "sku": "ROZ5588-1PCS",
      "quantity": 1
    }]
  }'
```

### Return Full Order
```bash
curl -X POST "${BASE_URL}/api/v1/return_full_order" \
  -H "Authorization: Bearer ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "customer_123",
    "order_id": "ORD123"
  }'
```

### Create Return (API)
```bash
curl -X POST "${BASE_URL}/api/v1/create_return" \
  -H "Authorization: ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "return_reason": "Defective product",
    "order_full_return": false,
    "items": [{
      "sku": "ROZ1602-1PCS",
      "quantity": 1
    }]
  }'
```

## Payment APIs (Token Validation)

### Create Payment Order (API) - `/api/v1/create_payment_order`
```bash
curl -X POST "${BASE_URL}/api/v1/create_payment_order" \
  -H "Authorization: ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD123",
    "amount": 299.99,
    "customer_id": "customer_123",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "+91-9876543210",
    "notes": {
      "description": "Payment for order ORD123"
    }
  }'
```

**Required Parameters:**
- `order_id`: OMS Order ID (string, 1-50 chars)
- `amount`: Order amount in rupees (float, > 0)
- `customer_id`: Customer ID (string, 1-50 chars)  
- `customer_name`: Customer name (string, 1-100 chars)

**Optional Parameters:**
- `customer_email`: Customer email for receipts
- `customer_phone`: Customer phone number
- `notes`: Additional notes object

### Verify Payment (API) - `/api/v1/verify_payment`
```bash
curl -X POST "${BASE_URL}/api/v1/verify_payment" \
  -H "Authorization: ${API_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "oms_order_id": "ORD123",
    "razorpay_order_id": "order_sample123",
    "razorpay_payment_id": "pay_sample123",
    "razorpay_signature": "sample_signature_hash"
  }'
```

**Required Parameters:**
- `oms_order_id`: OMS order ID
- `razorpay_order_id`: Razorpay order ID
- `razorpay_payment_id`: Razorpay payment ID  
- `razorpay_signature`: Razorpay signature for verification

### Get Payment Status (API) - `/api/v1/payment_status/{order_id}`
```bash
curl -X GET "${BASE_URL}/api/v1/payment_status/ORD123" \
  -H "Authorization: ${API_TOKEN}"
```

**Path Parameters:**
- `order_id`: OMS Order ID (in URL path)

**Response Format:**
```json
{
  "success": true,
  "order_id": "ORD123",
  "order_status": "open",
  "payment_summary": {
    "total_payments": 1,
    "total_amount": 299.99,
    "payment_status": "completed"
  }
}
```

## Refund APIs

### Get Refund Details by Phone
```bash
curl -X GET "${BASE_URL}/api/v1/refund_details_by_phone?phone_number=9123456789" \
  -H "Authorization: ${API_TOKEN}"
```

## Webhook Endpoints

### Razorpay Webhook
```bash
curl -X POST "${BASE_URL}/webhooks/v1/razorpay_webhook" \
  -H "X-Razorpay-Signature: sample_webhook_signature" \
  -H "Content-Type: application/json" \
  -d '{
    "event": "payment.captured",
    "payload": {
      "payment": {
        "entity": {
          "id": "pay_sample123",
          "amount": 29999,
          "status": "captured",
          "notes": {
            "oms_order_id": "ORD123"
          }
        }
      }
    }
  }'
```

## Wallet API (External Service)

### Add Wallet Entry
```bash
curl -X POST "https://wallet-uat.rozana.tech/internal/wallet-entry/customer_id/add-entry" \
  -H "x-api-key: your-secure-internal-api-key" \
  -H "Content-Type: application/json" \
  -H "x-Geolocation: 12.934302590149795,77.63010364970812" \
  -d '{
    "related_id": "credit_001",
    "order_id": "order_123",
    "wallet_amt": 40,
    "entry_type": "credit",
    "description": "Adding money to wallet",
    "reference_type": "payment_reference"
  }'
```

## Common Parameters

### Pagination Parameters
- `page`: Page number (starting from 1)
- `page_size`: Number of items per page (default: 20)
- `sort_order`: Sort order ("asc" or "desc", default: "desc")

### Search Parameters
- `search`: Search orders by order ID
- `exclude_status`: Exclude orders with specific status (e.g., "10")

### Order Status Values
- `pending`: Order pending
- `confirmed`: Order confirmed
- `processing`: Order processing
- `shipped`: Order shipped
- `delivered`: Order delivered
- `cancelled`: Order cancelled
- `returned`: Order returned

### Payment Modes
- `cod`: Cash on Delivery
- `prepaid`: Prepaid/Online Payment
- `cash`: Cash (POS)
- `card`: Card Payment
- `wallet`: Wallet Payment

## Error Responses

All endpoints return standard HTTP status codes:
- `200`: Success
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (invalid token)
- `404`: Not Found (resource not found)
- `500`: Internal Server Error

Error response format:
```json
{
  "detail": "Error message description"
}
```

## Testing Tips

1. **Authentication**: Always ensure you have valid tokens before testing endpoints
2. **Order IDs**: Use valid order IDs that exist in your system
3. **Customer IDs**: Ensure customer IDs match Firebase user IDs for app/pos endpoints
4. **Facility Names**: Use correct facility names that exist in your system
5. **SKU Codes**: Use valid SKU codes from your inventory

## Environment Variables

Set these variables for different environments:

### Development
```bash
export BASE_URL="http://localhost:8000"
```

### Staging
```bash
export BASE_URL="https://oms-uat.rozana.tech"
```

### Production
```bash
export BASE_URL="https://oms.rozana.tech"
```
