"""added cancelled qty and the refunded qty

Revision ID: 3590a59c2eeb
Revises: 395a2344f938
Create Date: 2025-09-02 16:47:01.583401

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3590a59c2eeb'
down_revision: Union[str, Sequence[str], None] = '395a2344f938'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('cancelled_quantity', sa.DECIMAL(precision=10, scale=2), server_default='0', nullable=True))
    op.add_column('order_items', sa.Column('refunded_quantity', sa.DECIMAL(precision=10, scale=2), server_default='0', nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('order_items', 'refunded_quantity')
    op.drop_column('order_items', 'cancelled_quantity')
    # ### end Alembic commands ###
