"""Modify return table

Revision ID: 395a2344f938
Revises: 8460c42c4c8b
Create Date: 2025-09-02 05:17:42.186968

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '395a2344f938'
down_revision: Union[str, Sequence[str], None] = '8460c42c4c8b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('returns', sa.Column('comments', sa.Text(), nullable=True))
    op.alter_column('returns', 'return_reason',
               existing_type=sa.TEXT(),
               type_=sa.String(length=100),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('returns', 'return_reason',
               existing_type=sa.String(length=100),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.drop_column('returns', 'comments')
    # ### end Alembic commands ###
