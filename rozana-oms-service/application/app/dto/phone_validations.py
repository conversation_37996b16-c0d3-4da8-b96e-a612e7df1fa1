from pydantic import BaseModel, field_validator
import re


class PhoneNumberValidator(BaseModel):
    phone_number: str
    
    @field_validator('phone_number')
    @classmethod
    def validate_phone_number(cls, v):
        if not v:
            raise ValueError('Incorrect Phone number')
        
        # Remove non-digits except +
        cleaned = re.sub(r'[^\d+]', '', v)
        
        # Validate and normalize to +91XXXXXXXXXX format
        if re.match(r'^\d{10}$', cleaned):
            return f'+91{cleaned}'
        elif re.match(r'^91\d{10}$', cleaned):
            return f'+{cleaned}'
        elif re.match(r'^\+91\d{10}$', cleaned):
            return cleaned
        elif re.match(r'^0\d{10}$', cleaned):
            return f'+91{cleaned[1:]}'
        
        raise ValueError('Invalid phone number format. Expected: 10 digits, 91+10 digits, or +91+10 digits')


def validate_phone_number(phone: str) -> str:
    return PhoneNumberValidator(phone_number=phone).phone_number