from fastapi import APIRouter, Request, Query, BackgroundTasks
from app.dto.orders import OrderCreate, OrderResponse  # type: ignore
from app.dto.orders import OrderStatusUpdate, OrderItemStatusUpdate  # type: ignore
from app.core.order_functions import (
    create_order_core,
    get_order_details_core,
    get_all_facility_orders_core,
)
from app.core.order_updates import (
    update_order_status_core,
    update_item_status_core,
)
from app.validations.orders import OrderFacilityValidator

pos_router = APIRouter(tags=["pos"])


@pos_router.post("/create_order", response_model=OrderResponse)
async def create_order(order: OrderCreate, request: Request, background_tasks: BackgroundTasks):
    """Create order via POS system."""
    return await create_order_core(order, request, background_tasks, "pos")


@pos_router.get("/order_details")
async def get_order_details(facility_name: str = Query(..., description="Facility name"), order_id: str = Query(..., description="Order ID")):
    """Retrieve order details via POS system."""
    order_validator = OrderFacilityValidator(order_id=order_id, facility_name=facility_name)
    order_validator.validate_order_with_facility()
    return await get_order_details_core(order_id)


@pos_router.get("/orders")
async def get_all_orders(facility_name: str = Query(..., description="Facility name"), page_size: int = 20, page: int = 1, sort_order: str = "desc", search: str = Query(None, description="Search orders by order ID")):
    """List orders for a logged-in POS user with optional search functionality."""
    return await get_all_facility_orders_core(facility_name, page_size, page, sort_order, search)

# ---- Update endpoints ----
@pos_router.put("/update_order_status")
@pos_router.patch("/update_order_status")
async def update_order_status(order_update: OrderStatusUpdate):
    """Update order status (POS)."""
    return await update_order_status_core(order_update)


@pos_router.put("/update_item_status")
@pos_router.patch("/update_item_status")
async def update_item_status(item_update: OrderItemStatusUpdate):
    """Update item status (POS)."""
    return await update_item_status_core(item_update)
