from fastapi import APIRouter, HTTPException

from app.core.order_return import create_return_core
from app.dto.returns import CreateR<PERSON>urnRequest
from app.logging.utils import get_app_logger

pos_router = APIRouter(tags=["pos"])
logger = get_app_logger(__name__)

@pos_router.post("/create_return")
async def pos_create_return(req: CreateReturnRequest):
     """
     POS-compatible create_return endpoint mirroring the App's behavior.
     Accepts the same payload as /app/v1/create_return and delegates to core.
     """
     # Data Extraction (same as app route)
     items_to_return = [{"sku": i.sku, "quantity": i.quantity} for i in (req.items or [])]
     order_full_return = bool(getattr(req, "order_full_return", False))
     order_id = req.order_id
     return_reason = req.return_reason_code if req.return_reason_code else "OTHER"
     comments = req.comments if req.comments else " "

     try:
         return await create_return_core(
             order_id=order_id,
             items=items_to_return,
             order_full_return=order_full_return,
             return_reason=return_reason,
             comments=comments,
         )
     except HTTPException:
         raise
     except Exception as e:
         logger.error(
             f"Unexpected error in POS create_return for order {order_id}: {str(e)}",
             exc_info=True,
         )
         raise HTTPException(status_code=500, detail="Internal server error")
