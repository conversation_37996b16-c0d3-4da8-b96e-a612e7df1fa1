"""
Payment repository for raw SQL operations
"""

from app.connections.database import get_raw_transaction
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Dict, Any, Optional
from sqlalchemy import text
from app.core.constants import PaymentStatus

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger("app.payment_repository")

# Define IST timezone
IST = timezone(timedelta(hours=5, minutes=30))


class PaymentRepository:
    """Repository for payment-related database operations using raw SQL"""

    def create_payment_record(
        self,
        order_id: int,
        payment_id: str,
        payment_amount: Decimal,
        payment_mode: str,
        payment_status: int = PaymentStatus.PENDING,
        total_amount: Optional[Decimal] = None,
        payment_order_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Create a payment record using raw SQL.
        Preserves Decimal precision for monetary values.
        """
        payment_date = datetime.now(IST)
        effective_total_amount = total_amount or payment_amount

        insert_query = text("""
            INSERT INTO payment_details (
                order_id, payment_id, payment_amount, payment_date, 
                payment_mode, payment_status, total_amount, payment_order_id,
                created_at, updated_at
            ) VALUES (
                :order_id, :payment_id, :payment_amount, :payment_date,
                :payment_mode, :payment_status, :total_amount, :payment_order_id,
                :created_at, :updated_at
            )
            RETURNING id, created_at
        """)

        try:
            with get_raw_transaction() as conn:
                result = conn.execute(insert_query, {
                    "order_id": order_id,
                    "payment_id": payment_id,
                    "payment_amount": payment_amount,
                    "payment_date": payment_date,
                    "payment_mode": payment_mode,
                    "payment_status": payment_status,
                    "total_amount": effective_total_amount,
                    "payment_order_id": payment_order_id,
                    "created_at": payment_date,
                    "updated_at": payment_date,
                })

                # fetch result before commit
                row = result.fetchone()
                conn.commit()

                payment_record_id, created_at = row if row else (None, None)

            logger.info(
                f"payment_record_created | id={payment_record_id} "
                f"order_id={order_id} payment_id={payment_id} "
                f"mode={payment_mode} status={payment_status} "
                f"amount={payment_amount}"
            )

            return {
                "success": True,
                "payment_record_id": payment_record_id,
                "payment_id": payment_id,
                "order_id": order_id,
                "payment_amount": payment_amount,
                "payment_mode": payment_mode,
                "payment_status": payment_status,
                "created_at": created_at.isoformat() if created_at else None,
            }

        except Exception as e:
            try:
                conn.rollback()
            except Exception:
                pass
            logger.error(
                f"payment_record_create_error | order_id={order_id} "
                f"payment_id={payment_id} mode={payment_mode} error={e}",
                exc_info=True,
            )
            raise e
