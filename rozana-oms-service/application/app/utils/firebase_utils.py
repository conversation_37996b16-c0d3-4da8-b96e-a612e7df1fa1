"""
Firebase utilities for customer operations
"""
import firebase_admin
from firebase_admin import auth
from fastapi import HTTPException
from typing import Optional
import re

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger(__name__)

# Get Firebase app instance
app_instance = firebase_admin.get_app("app")

async def get_customer_id_from_phone_number(phone_number: str) -> str:
    """
    Get customer ID from phone number using Firebase Auth.
    
    Args:
        phone_number: Phone number to lookup customer (should include country code)
        
    Returns:
        str: Customer ID (Firebase UID)
        
    Raises:
        HTTPException: If customer not found or Firebase call fails
    """
    try:
        user_record = auth.get_user_by_phone_number(phone_number, app=app_instance)        
        customer_id = user_record.uid
        logger.info(f"Successfully retrieved customer ID: {customer_id} for phone: {phone_number}")
        
        return customer_id
        
    except auth.UserNotFoundError:
        logger.warning(f"Customer not found for phone number: {phone_number}")
        raise HTTPException(status_code=404, detail="Customer not found")
    except ValueError as e:
        logger.warning(f"Invalid phone number format: {phone_number} - {e}")
        raise HTTPException(status_code=400, detail="Invalid phone number format")
    except Exception as e:
        logger.error(f"Firebase error getting customer for phone {phone_number}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve customer information") from e
