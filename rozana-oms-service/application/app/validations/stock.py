from app.connections.redis_wrapper import RedisJ<PERSON><PERSON><PERSON>rapper, RedisKeyProcessor


class StockValidator:
    def __init__(self, warehouse: str, sku: str):
        self.warehouse = warehouse
        self.sku = sku
        self.redis_key_processor = RedisKeyProcessor()
        self.redis_key = self.redis_key_processor._stock_key(self.warehouse, self.sku)

    def get_stock(self):
        redis_wrapper = RedisJSONWrapper()
        stock = redis_wrapper.get(self.redis_key)
        if stock is None:
            raise ValueError(f"Stock not found for facility {self.warehouse} and sku {self.sku}")
        return stock["data"]

    def validate_stock(self, quantity: int=1):
        stock_data = self.get_stock()
        if stock_data is None:
            raise ValueError(f"Stock not found for facility {self.warehouse} and sku {self.sku}")
        available_stock = stock_data.get("available_quantity", 0)
        if available_stock < quantity:
            raise ValueError(f"Stock not available for facility {self.warehouse} and sku {self.sku}")
        return True

    def block_stock(self, quantity: int):
        stock_data = self.get_stock()
        available_stock = stock_data.get("available_quantity", 0)
        new_available_stock = available_stock
        if available_stock < quantity:
            raise ValueError(f"Not enough stock available for facility {self.warehouse} and sku {self.sku}")

        new_available_stock = available_stock - quantity
        stock_data["available_quantity"] = new_available_stock
        redis_wrapper = RedisJSONWrapper()
        update_stock_data = {"data": stock_data}
        redis_wrapper.set(self.redis_key, update_stock_data)
        return new_available_stock

