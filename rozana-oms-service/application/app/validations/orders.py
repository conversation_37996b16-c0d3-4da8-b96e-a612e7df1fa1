from app.dto.orders import OrderCreate
from fastapi import HTTPException
from app.connections.database import execute_raw_sql_readonly


class OrderCreateValidator:
    def __init__(self, order: OrderCreate = None, user_id: str = None):
        self.order = order
        self.user_id = user_id

    def validate(self):
        self.validate_user_id_customer_id()
        self.validate_duplicate_sku_items()

    def validate_user_id_customer_id(self):
        if self.user_id != self.order.customer_id:
            raise ValueError("User ID and Customer ID do not match")

    def validate_page_size(self, page_size: int = 20, page: int = 1, max_page_size: int = 100):
        # Validate page starts from 1
        if page < 1:
            raise ValueError("Page number must be 1 or greater")

        # Validate page_size is reasonable
        if page_size < 1:
            raise ValueError("Page size must be at least 1")
        if page_size > max_page_size:
            raise ValueError(f"Page size must be between 1 and {max_page_size}")

    def validate_pagination_params(self, page_size: int = 20, page: int = 1, total_count: int = 0):
        """Validate pagination parameters including page bounds"""
        self.validate_page_size(page_size, page)

        # Calculate total pages
        total_pages = (total_count + page_size - 1) // page_size if page_size > 0 else 0

        # If we have data but page is too high, adjust to last page
        if total_count > 0 and page > total_pages:
            raise ValueError(f"Page {page} exceeds maximum page {total_pages} for {total_count} total items")

        return total_pages

    def validate_items_count(self):
        """Validate that the order has at least one item."""
        if not self.order.items or len(self.order.items) < 1:
            raise HTTPException(status_code=400, detail="Order must contain at least one item")

    def validate_duplicate_sku_items(self):
        sku_list = [item.sku for item in self.order.items]
        duplicates = [sku for sku in sku_list if sku_list.count(sku) > 1]
        if duplicates:
            raise ValueError(f"Duplicate SKU items found: {', '.join(set(duplicates))}")

    def validate_quantity(self, origin: str = "app"):
        if origin == "app" or origin == "api":
            # For app orders, quantity should be an integer
            invalid_skus = []
            for item in self.order.items:
                if item.quantity != int(item.quantity):
                    invalid_skus.append(item.sku)
                else:
                    # Normalize to int to avoid downstream float handling for app
                    item.quantity = int(item.quantity)
            
            # If any invalid SKUs found, raise error with all of them
            if invalid_skus:
                sku_list = ', '.join(invalid_skus)
                raise HTTPException(status_code=400, detail=f"For app orders, quantity must be an integer for SKUs: {sku_list}")


class OrderUserValidator:
    def __init__(self, order_id: str = None, user_id: str = None):
        self.order_id = order_id
        self.user_id = user_id

    def validate_order_with_user(self):
        query = """SELECT customer_id FROM orders WHERE order_id = :order_id"""
        params = {"order_id": self.order_id}
        result = execute_raw_sql_readonly(query, params)
        if not result or result[0].get('customer_id') != self.user_id:
            raise HTTPException(status_code=404, detail="Order not found or access denied")
        
class OrderFacilityValidator:
    def __init__(self, order_id: str = None, facility_name: str = None):
        self.order_id = order_id
        self.facility_name = facility_name
    
    def validate_order_with_facility(self):
        query = """SELECT facility_name FROM orders WHERE order_id = :order_id"""
        params = {"order_id":self.order_id}
        result = execute_raw_sql_readonly(query, params)
        if not result or result[0].get('facility_name') != self.facility_name:
            raise HTTPException(status_code=404, detail="Order not found or access denied")