"""
Payment service for handling all payment-related operations.

This service manages payment records in the payment_details table,
keeping payment status completely separate from order status.
"""

import os
from datetime import datetime, timezone
from decimal import Decimal
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.models.payments import PaymentDetails
from app.models.orders import Order
from app.connections.database import get_db_session, execute_raw_sql, get_raw_transaction
from app.core.constants import PaymentStatus, OrderStatus
from app.repository.payments import PaymentRepository

from app.services.order_service import OrderService
from app.integrations.potions_service import PotionsService

# Request context
from app.middlewares.request_context import request_context

# Logger
from app.logging.utils import get_app_logger
logger = get_app_logger('app.payment_service')

# Settings
from app.config.settings import OMSConfigs
configs = OMSConfigs()
RAZORPAY_KEY_ID = configs.RAZORPAY_KEY_ID

class PaymentService:
    """Service for managing payment operations"""

    def __init__(self):
        # Fix: PaymentRepository doesn't take any arguments
        self.payment_repo = PaymentRepository()
        # set module context for better traceability in logs
        try:
            request_context.module_name = 'payment_service'
        except Exception:
            pass

    async def create_payment_record(
        self,
        order_id: int,
        payment_id: str,
        payment_amount: Decimal,
        payment_mode: str,
        payment_status: int = PaymentStatus.PENDING,
        total_amount: Decimal = None,  # Add this parameter
        payment_order_id: str = None
    ) -> Dict[str, Any]:
        """
        Create a payment record in the payments table.
        Each payment mode gets its own record.
        
        Args:
            order_id: ID of the order this payment is for
            payment_id: External payment ID (e.g., Razorpay payment ID or generated ID for cash)
            payment_amount: Amount being paid for this payment mode
            payment_mode: Payment mode ('cash', 'online', 'cash_and_online')
            payment_status: Payment status
            total_amount: Total order amount (not individual payment amount)
        
        Returns:
            Dict with success status and payment details
        """
        try:
            # Create new payment record using repository
            payment_result = self.payment_repo.create_payment_record(
                order_id=order_id,
                payment_id=payment_id,
                payment_amount=payment_amount,
                payment_mode=payment_mode,
                payment_status=payment_status,
                total_amount=total_amount,
                payment_order_id=payment_order_id
            )

            data = {
                "success": True,
                "payment_order_id": payment_order_id,
                "payment_id": payment_id,
                "currency": "INR",
                "order_id": order_id,
                "amount": float(payment_amount),
                "amount_paise": int(payment_amount * 100),
                "payment_mode": payment_mode,
                "status": payment_result["payment_status"],
                "created_at": payment_result["created_at"]
            }

            if payment_mode == 'razorpay':
                data["razorpay_key_id"] = RAZORPAY_KEY_ID

            return data

        except Exception as e:
            logger.error(f"payment_record_create_error | order_id={order_id} payment_id={payment_id} mode={payment_mode} error={e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error creating payment record: {str(e)}"
            }

    async def update_payment_status(
        self,
        payment_id: str,
        new_status: int
    ) -> Dict[str, Any]:
        """
        Update only the payment status for an existing payment record.
        This is the correct way to update payment status from webhooks.
        Payment details (amount, mode, etc.) are immutable once created.
        Args:
            payment_id: External payment ID (e.g., Razorpay payment ID)
            new_status: New payment status code
        Returns:
            Dict with success status and updated payment details
        """
        try:
            with get_raw_transaction() as db:
                # Find existing payment record
                find_result = db.execute(text("""
                    SELECT id, payment_id, payment_status, updated_at
                    FROM payment_details
                    WHERE payment_id = :payment_id
                    LIMIT 1
                """), {"payment_id": payment_id})

                payment_row = find_result.fetchone()

                if not payment_row:
                    logger.warning(f"payment_record_not_found | payment_id={payment_id}")
                    return {
                        "success": False,
                        "message": f"Payment record not found for payment_id {payment_id}"
                    }

                # Extract data immediately while connection is open
                payment_data = dict(payment_row._mapping)
                old_status = payment_data['payment_status']

                # Update payment status in same transaction
                updated_at = datetime.now(timezone.utc)
                db.execute(text("""
                    UPDATE payment_details
                    SET payment_status = :new_status,
                        updated_at = :updated_at
                    WHERE payment_id = :payment_id
                """), {
                    "new_status": new_status,
                    "updated_at": updated_at,
                    "payment_id": payment_id
                })

                logger.info(f"payment_status_updated | payment_id={payment_id} old_status={old_status} new_status={new_status}")

                return {
                    "success": True,
                    "payment_record_id": payment_data['id'],
                    "payment_id": payment_id,
                    "old_status": old_status,
                    "new_status": new_status,
                    "status_display": PaymentStatus.get_description(new_status),
                    "updated_at": updated_at.isoformat()
                }

        except Exception as e:
            logger.error(f"payment_status_update_error | payment_id={payment_id} new_status={new_status} error={e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error updating payment status: {str(e)}"
            }

    async def get_payment_by_id(self, payment_id: str) -> Optional[Dict[str, Any]]:
        """Get payment record by external payment ID"""
        try:
            query = """
                SELECT id, order_id, payment_order_id, payment_id, payment_amount,
                       payment_date, payment_mode, payment_status, total_amount,
                       created_at, updated_at
                FROM payment_details
                WHERE payment_id = :payment_id
                LIMIT 1
            """

            result = execute_raw_sql(query, {"payment_id": payment_id})
            return result[0] if result else None
        except Exception as e:
            logger.error(f"payment_fetch_error | payment_id={payment_id} error={e}", exc_info=True)
            return None

    async def get_payments_for_order(self, order_id: int) -> List[Dict[str, Any]]:
        """Get all payment records for an order"""
        try:
            query = """
                SELECT id, order_id, payment_order_id, payment_id, payment_amount,
                       payment_date, payment_mode, payment_status, total_amount,
                       created_at, updated_at
                FROM payment_details
                WHERE order_id = :order_id
                ORDER BY created_at DESC
            """

            return execute_raw_sql(query, {"order_id": order_id})
        except Exception as e:
            logger.error(f"order_payments_fetch_error | order_id={order_id} error={e}", exc_info=True)
            return []

    async def update_pending_payment_with_razorpay_details(
        self,
        order_id: int,
        razorpay_payment_id: str,
        payment_amount: float,
        payment_status: int
    ) -> Dict[str, Any]:
        """
        Update existing pending payment record with Razorpay details instead of creating duplicate.
        Args:
            order_id: Internal order ID
            razorpay_payment_id: Razorpay payment ID
            payment_amount: Payment amount from Razorpay
            payment_status: New payment status
        Returns:
            Dict with success status and updated payment details
        """
        try:
            with get_raw_transaction() as db:
                # Find the existing pending payment record for this order with razorpay mode
                pending_payment_result = db.execute(text("""
                    SELECT id, order_id, payment_order_id, payment_id, payment_amount,
                           payment_date, payment_mode, payment_status, total_amount,
                           created_at, updated_at
                    FROM payment_details
                    WHERE order_id = :order_id
                      AND payment_mode = 'razorpay'
                      AND payment_status = :pending_status
                    LIMIT 1
                """), {
                    "order_id": order_id,
                    "pending_status": PaymentStatus.PENDING
                })

                pending_row = pending_payment_result.fetchone()
                
                if not pending_row:
                    logger.warning(f"pending_payment_not_found | order_id={order_id}")
                    return {
                        "success": False,
                        "message": f"No pending online payment found for order {order_id}"
                    }
                
                # Extract data immediately while connection is open
                pending_payment_data = dict(pending_row._mapping)
                old_payment_id = pending_payment_data['payment_id']

                # Update the existing record with Razorpay details
                updated_at = datetime.now(timezone.utc)
                db.execute(text("""
                    UPDATE payment_details 
                    SET payment_id = :razorpay_payment_id,
                        payment_amount = :payment_amount,
                        payment_status = :payment_status,
                        updated_at = :updated_at
                    WHERE id = :payment_record_id
                """), {
                    "razorpay_payment_id": razorpay_payment_id,
                    "payment_amount": Decimal(str(payment_amount)),
                    "payment_status": payment_status,
                    "updated_at": updated_at,
                    "payment_record_id": pending_payment_data['id']
                })

                logger.info(f"pending_payment_updated | order_id={order_id} old_payment_id={old_payment_id} new_payment_id={razorpay_payment_id} amount={float(payment_amount)} payment_status={payment_status}")

                return {
                    "success": True,
                    "action": "updated",
                    "payment_record_id": pending_payment_data['id'],
                    "old_payment_id": old_payment_id,
                    "new_payment_id": razorpay_payment_id,
                    "order_id": order_id,
                    "amount": float(payment_amount),
                    "status": payment_status,
                    "status_display": PaymentStatus.get_description(payment_status),
                    "updated_at": updated_at.isoformat()
                }

        except Exception as e:
            logger.error(f"pending_payment_update_error | order_id={order_id} payment_id={razorpay_payment_id} error={e}", exc_info=True)
            return {
                "success": False,
                "message": f"Error updating pending payment: {str(e)}"
            }

    async def get_payment_status_for_order(self, order_id: int) -> Dict[str, Any]:
        """
        Get payment status summary for an order.
        Returns:
            Dict with payment status information for the order
        """
        try:
            payments = await self.get_payments_for_order(order_id)

            if not payments:
                return {
                    "order_id": order_id,
                    "has_payments": False,
                    "payment_status": None,
                    "total_paid": 0.0,
                    "payment_count": 0
                }

            # Calculate totals and determine overall status
            total_paid = sum(
                float(p['payment_amount']) for p in payments 
                if p['payment_status'] == PaymentStatus.COMPLETED
            )

            # Categorize payments by status
            completed_payments = [p for p in payments if p['payment_status'] == PaymentStatus.COMPLETED]
            pending_payments = [p for p in payments if p['payment_status'] == PaymentStatus.PENDING]
            failed_payments = [p for p in payments if p['payment_status'] == PaymentStatus.FAILED]
            refunded_payments = [p for p in payments if p['payment_status'] == PaymentStatus.REFUNDED]

            # Determine overall payment status with proper priority:
            # 1. If ANY payment is completed -> Overall status is COMPLETED
            # 2. If no completed but has pending -> Overall status is PENDING  
            # 3. If no completed/pending but has refunded -> Overall status is REFUNDED
            # 4. Only if ALL payments failed -> Overall status is FAILED
            if completed_payments:
                overall_status = PaymentStatus.COMPLETED
                logger.info(f"order_payment_summary | order_id={order_id} overall_status=COMPLETED completed_count={len(completed_payments)}")
            elif pending_payments:
                overall_status = PaymentStatus.PENDING
                logger.info(f"order_payment_summary | order_id={order_id} overall_status=PENDING pending_count={len(pending_payments)}")
            elif refunded_payments:
                overall_status = PaymentStatus.REFUNDED
                logger.info(f"order_payment_summary | order_id={order_id} overall_status=REFUNDED refunded_count={len(refunded_payments)}")
            else:
                overall_status = PaymentStatus.FAILED
                logger.info(f"order_payment_summary | order_id={order_id} overall_status=FAILED")

            return {
                "order_id": order_id,
                "has_payments": True,
                "payment_status_display": PaymentStatus.get_description(overall_status),
                "total_paid": total_paid,
                "payment_count": len(payments),
                "completed_count": len(completed_payments),
                "pending_count": len(pending_payments),
                "failed_count": len(failed_payments),
                "refunded_count": len(refunded_payments),
                "payments": [
                    {
                        "payment_id": payment['payment_id'],
                        "payment_status_display": PaymentStatus.get_description(payment['payment_status']),
                        "payment_amount": float(payment['payment_amount']),
                        "payment_mode": payment['payment_mode'],
                        "created_at": payment['created_at'].isoformat() if payment['created_at'] else None,
                        "updated_at": payment['updated_at'].isoformat() if payment['updated_at'] else None
                    }
                    for payment in payments
                ]
            }

        except Exception as e:
            logger.error(f"order_payment_status_error | order_id={order_id} error={e}", exc_info=True)
            return {
                "order_id": order_id,
                "has_payments": False,
                "payment_status": None,
                "total_paid": 0.0,
                "payment_count": 0,
                "error": str(e)
            }
    
    async def check_and_complete_order_if_all_payments_successful(self, order_id: int, background_tasks=None) -> Dict[str, Any]:
        """
        Check if all payments for an order are successful and update order status to CONFIRMED.
        Also triggers WMS sync via Potions service.
        
        Args:
            order_id: Internal order ID
            
        Returns:
            Dict with completion status and details
        """
        try:
            logger.info(f"Checking payment completion for order {order_id}")
            
            # Get all payments for the order
            payments = await self.get_payments_for_order(order_id)
            
            if not payments:
                return {
                    "success": False,
                    "message": f"No payments found for order {order_id}",
                    "order_completed": False
                }
            
            # Use raw SQL to update cash payments to avoid connection conflicts
            for payment in payments:
                if payment['payment_mode'] == 'cash' and payment['payment_status'] != PaymentStatus.COMPLETED:
                    updated_at = datetime.now(timezone.utc)
                    update_query = """
                        UPDATE payment_details 
                        SET payment_status = :new_status,
                            updated_at = :updated_at
                        WHERE id = :payment_id
                    """
                    
                    execute_raw_sql(update_query, {
                        "new_status": PaymentStatus.COMPLETED,
                        "updated_at": updated_at,
                        "payment_id": payment['id']
                    })
                    logger.info(f"Updated cash payment {payment['payment_id']} to COMPLETED status")
                    
                    # Update the payment dict for subsequent checks
                    payment['payment_status'] = PaymentStatus.COMPLETED

            # Check if all payments are completed
            all_payments_successful = all(
                p['payment_status'] == PaymentStatus.COMPLETED 
                for p in payments
            )

            if all_payments_successful:
                # Update order status to CONFIRMED
                # Get order details for WMS sync and update status
                get_order_query = """
                    SELECT id, order_id, facility_name, customer_id, status
                    FROM orders
                    WHERE id = :order_id
                    LIMIT 1
                """
                order_result = execute_raw_sql(get_order_query, {"order_id": order_id})

                if not order_result:
                    logger.warning(f"Order not found for id {order_id} while completing payments")
                    return {
                        "success": False,
                        "message": f"Order {order_id} not found",
                        "order_completed": False
                    }

                order_row = order_result[0]
                order_service = OrderService()
                order_external_id = order_row['order_id']

                await order_service.update_order_status(order_external_id, OrderStatus.OPEN)
                logger.info(f"Updated order {order_external_id} status to OPEN")

                # Trigger WMS sync via Potions service
                facility_name = order_row['facility_name']

                if facility_name and order_external_id:
                    potions_service = PotionsService()
                    if background_tasks:
                        # Schedule WMS sync as background task
                        background_tasks.add_task(
                            potions_service.sync_order_by_id,
                            facility_name,
                            order_external_id,
                            order_service
                        )
                        logger.info(f"Scheduled WMS sync for order {order_external_id}")
                    else:
                        # Execute WMS sync immediately if no background tasks available
                        try:
                            sync_result = await potions_service.sync_order_by_id(
                                facility_name,
                                order_external_id,
                                order_service
                            )
                            logger.info(f"WMS sync triggered for order {order_external_id}: {sync_result}")
                        except Exception as sync_error:
                            logger.error(f"WMS sync failed for order {order_external_id}: {sync_error}")
                
                return {
                    "success": True,
                    "message": f"All payments completed and order {order_id} status updated to OPEN",
                    "order_completed": True,
                    "order_status": "OPEN",
                    "total_payments": len(payments),
                    "completed_payments": len([p for p in payments if p['payment_status'] == PaymentStatus.COMPLETED])
                }
            else:
                pending_payments = [p for p in payments if p['payment_status'] == PaymentStatus.PENDING]
                failed_payments = [p for p in payments if p['payment_status'] == PaymentStatus.FAILED]
                
                logger.info(f"Order {order_id} has incomplete payments: {len(pending_payments)} pending, {len(failed_payments)} failed")
                
                return {
                    "success": True,
                    "message": f"Order {order_id} still has incomplete payments",
                    "order_completed": False,
                    "order_status": "PENDING",
                    "total_payments": len(payments),
                    "completed_payments": len([p for p in payments if p['payment_status'] == PaymentStatus.COMPLETED]),
                    "pending_payments": len(pending_payments),
                    "failed_payments": len(failed_payments)
                }
            
        except Exception as e:
            
            logger.error(f"Error checking payment completion for order {order_id}: {e}")
            return {
                "success": False,
                "message": f"Error checking payment completion: {str(e)}",
                "order_completed": False
            }

