name: Deploy OMS to UAT

on:
  workflow_dispatch:
    inputs:
      version:
          description: "Image version tag (default: latest)"
          required: false
          default: latest

jobs:
  deploy:
    runs-on: ims-runner
    timeout-minutes: 30
    env:
      AWS_REGION: ${{ vars.AWS_REGION }}
      ECS_API_CLUSTER: rozana-cluster-uat
      ECS_API_SERVICE: rozana-oms-uat-service
      ECS_API_TASK: rozana-oms-uat
      ECR_REGISTRY: ${{ vars.ECR_REGISTRY }}
      ECR_IMAGE_TAG: ${{ github.event.inputs.version }}
      ECR_REPOSITORY:  ${{ vars.ECR_REPOSITORY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Ensure deploy script is executable
        run: chmod +x scripts/deploy-service.sh

      - name: Deploy OMS service
        run: ./scripts/deploy-service.sh "$ECS_API_CLUSTER" "$ECS_API_SERVICE" "$ECS_API_TASK" "$ECR_REGISTRY/$ECR_REPOSITORY:$ECR_IMAGE_TAG" "$ECR_IMAGE_TAG" "rozana-oms"
