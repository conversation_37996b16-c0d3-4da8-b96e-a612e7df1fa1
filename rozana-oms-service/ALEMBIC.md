# Alembic Migrations (Quick Guide)

Apply latest migrations:

```bash
alembic upgrade head
```

Create a new migration (after modifying models):

```bash
alembic revision --autogenerate -m "your message"
```

Downgrade to previous migration:

```bash
alembic downgrade -1
```

Downgrade to a specific migration:

```bash
alembic downgrade 4b6a7c487108
```

That's it — keep every schema change in a migration and always run `upgrade head` before coding.
