# WMS Redis API

A Go Fiber API for retrieving facility and SKU data from Redis with support for filtering.

## Features

- Fast, lightweight API built with Go Fiber framework
- Redis integration for data storage
- Filter inventory data by facility and SKU codes
- Support for both single and multiple SKU requests
- Available quantity tracking for each SKU
- Docker and <PERSON>er Compose ready for easy deployment
- Environment variable configuration

## API Endpoints

### Get Stock by Single SKU
- **GET** `/api/stock/:sku`
- URL parameter: `sku` - The SKU code to look up
- Required query param: `facility` - Filter by facility
- Returns stock data for a specific SKU in the specified facility

### Get Stock by Multiple SKUs
- **GET** `/api/stock/multi?skus=SKU1,SKU2,SKU3`
- Query parameter: `skus` - Comma-separated list of SKU codes
- Required query param: `facility` - Filter by facility
- Returns stock data for multiple SKUs in the specified facility

### Health Check
- **GET** `/api/health`
- Returns API health status with a simple `{"status": "ok"}` response

## Installation

### Prerequisites
- Go 1.16 or higher
- Redis server
- Docker and <PERSON>er Compose (optional)

### Environment Variables
- `SERVER_PORT`: Port for the API server (default: 8080)
- `REDIS_URL`: Redis connection URL (format: `redis://:password@host:port`)

### Local Setup

1. Clone the repository
2. Navigate to the project directory
3. Copy the environment file:
   ```bash
   cp .env.example .env
   ```
4. Modify the `.env` file as needed
5. Run the application:
   ```bash
   go mod tidy
   go run main.go
   ```

### Docker Setup

1. Clone the repository
2. Navigate to the project directory
3. Build and run with Docker Compose:
   ```bash
   docker-compose up -d
   ```

The API will be available at http://localhost:8080/api/

## Redis Data Structure

The application supports two Redis data structures:

### Hash Structure (Preferred)

Keys are structured as facility identifiers:

```
stock:<facility>
```

Each key is a Redis hash where fields are SKU codes and values are JSON strings:

```
HGET stock:TEST_WH1 SKU-1
```

Returns JSON data:

```json
{
  "sku_code": "SKU-1",
  "facility": "TEST_WH1",
  "quantity": 100,
  "available_quantity": 127,
  "last_update": "2023-06-25T12:00:00Z"
}
```

## Sample Requests

### Get stock for a single SKU (warehouse is mandatory)

```bash
curl http://localhost:8080/api/stock/SKU123?warehouse=WH001
```

### Get stock for multiple SKUs via GET (warehouse is mandatory)

```bash
curl http://localhost:8080/api/stock/multi?sku_codes=SKU123,SKU456&warehouse=WH001
```

### Get stock for multiple SKUs via POST (warehouse is mandatory)

```bash
curl -X POST http://localhost:8080/api/stock/multi \
  -H "Content-Type: application/json" \
  -d '{"sku_codes": ["SKU123", "SKU456"], "warehouse": "WH001"}'
```

## Development

To add seed data to Redis for testing:

```bash
docker exec -it go-redis-api redis-cli
> SET stock:SKU123:WH001 '{"sku_code":"SKU123","warehouse":"WH001","quantity":100,"last_update":"2023-06-25T12:00:00Z"}'
> SET stock:SKU456:WH001 '{"sku_code":"SKU456","warehouse":"WH001","quantity":200,"last_update":"2023-06-25T12:00:00Z"}'
> SET stock:SKU123:WH002 '{"sku_code":"SKU123","warehouse":"WH002","quantity":50,"last_update":"2023-06-25T12:00:00Z"}'
```

docker exec -it go-redis-api redis-cli
> SET stock:ROZANA_TEST_WH1:ROZ2649-1PCS '{"data": {"sku_code": "ROZ2649-1PCS", "sku_desc": "Parle Krack Jack Biscuit - Rs 10", "seller_id": null, "seller_name": null, "cost_price": 0.0, "batch_based": 1, "is_scannable": 0, "serial_numbers": [], "total_quantity": 30, "reserved_quantity": 0, "open_order_quantity": 0, "asn_quantity": 0, "putaway_quantity": 0, "cycle_stock_quantity": 0, "staging_stock_quantity": 0, "available_quantity": 30, "batch_details": [], "force_sync": true}}
'

docker exec -it 9728bad1469f redis-cli SET stock:ROZANA_TEST_WH1:ROZ7322-1PCS '{"data": {"sku_code": "ROZ7322-1PCS", "sku_desc": "Parle Krack Jack Biscuit - Rs 10", "seller_id": null, "seller_name": null, "cost_price": 0.0, "batch_based": 1, "is_scannable": 0, "serial_numbers": [], "total_quantity": 30, "reserved_quantity": 0, "open_order_quantity": 0, "asn_quantity": 0, "putaway_quantity": 0, "cycle_stock_quantity": 0, "staging_stock_quantity": 0, "available_quantity": 30, "batch_details": [], "force_sync": true}}'