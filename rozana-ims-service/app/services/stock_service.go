package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/url"
	"strconv"
	"strings"

	"github.com/go-redis/redis/v8"
	"github.com/wms/go-redis-api/models"
)

// StockService provides stock lookup operations using key pattern
// stock:{facility}:{sku}. Both dynamic parts are URL-escaped.
type StockService struct {
	redis          *redis.Client
	safetyQuantity float64
}

func NewStockService(client *redis.Client, safetyQuantity float64) *StockService {
	return &StockService{
		redis:          client,
		safetyQuantity: safetyQuantity,
	}
}

// key builds a Redis key for facility+sku following the format: stock:{facility}:{sku}
func key(facility, sku string) string {
	return "stock:" + url.QueryEscape(facility) + ":" + url.QueryEscape(sku)
}

// GetStockBySKU returns inventory for a single SKU at a facility.
func (s *StockService) GetStockBySKU(ctx context.Context, skuCode, facility string) ([]models.Stock, error) {
	val, err := s.redis.Get(ctx, key(facility, skuCode)).Result()
	if err == redis.Nil {
		return []models.Stock{}, nil
	}
	if err != nil {
		return nil, err
	}
	var raw map[string]interface{}
	if err := json.Unmarshal([]byte(val), &raw); err != nil {
		return nil, err
	}

	// Extract warehouse from root level
	warehouseName, _ := raw["warehouse"].(string)

	// Extract data from nested structure
	dataMap, ok := raw["data"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid payload structure - missing 'data' field")
	}

	st := models.Stock{
		SKUCode:  strings.TrimSpace(fmt.Sprint(dataMap["sku_code"])),
		Facility: warehouseName, // Use actual warehouse name from data
	}

	// Handle total_quantity (can be float64 or string)
	if tq, ok := dataMap["total_quantity"].(float64); ok {
		st.Quantity = int(tq)
	} else if tqStr, ok := dataMap["total_quantity"].(string); ok {
		if tqFloat, err := strconv.ParseFloat(tqStr, 64); err == nil {
			st.Quantity = int(tqFloat)
		}
	}

	// Handle available_quantity (can be float64 or string)
	if aq, ok := dataMap["available_quantity"].(float64); ok {
		st.AvailableQuantity = aq
	} else if aqStr, ok := dataMap["available_quantity"].(string); ok {
		if aqFloat, err := strconv.ParseFloat(aqStr, 64); err == nil {
			st.AvailableQuantity = aqFloat
		}
	}

	// Fallback to provided values if data is missing
	if st.SKUCode == "" {
		st.SKUCode = skuCode
	}
	if st.Facility == "" {
		st.Facility = facility
	}

	return []models.Stock{st}, nil
}

// GetStockVariants calculates variant quantities for a SKU
func (s *StockService) GetStockVariants(ctx context.Context, skuCode, facility string, variants []string) (models.VariantResponse, error) {
	// Get base stock data
	stocks, err := s.GetStockBySKU(ctx, skuCode, facility)
	if err != nil {
		return nil, err
	}

	if len(stocks) == 0 {
		return nil, fmt.Errorf("SKU not found")
	}

	baseQty := stocks[0].AvailableQuantity

	// Apply safety quantity - subtract from available quantity to maintain safety stock
	// Safety quantity is not included in customer-facing availability
	log.Printf("DEBUG: Base quantity for SKU '%s': %f", skuCode, baseQty)
	log.Printf("DEBUG: Safety quantity: %f", s.safetyQuantity)
	adjustedQty := baseQty - s.safetyQuantity
	log.Printf("DEBUG: Adjusted quantity: %f", adjustedQty)
	if adjustedQty < 0 {
		adjustedQty = 0
	}

	response := make(models.VariantResponse)

	for _, variantStr := range variants {
		variant, err := strconv.Atoi(strings.TrimSpace(variantStr))
		if err != nil {
			return nil, fmt.Errorf("invalid variant value: %s", variantStr)
		}

		// Validate variant range (>0 and <50)
		if variant <= 0 || variant >= 50 {
			return nil, fmt.Errorf("variant must be greater than 0 and less than 50: %d", variant)
		}

		// Calculate using floor division: adjustedQty / variant (after safety quantity deduction)
		calculatedQty := int(math.Floor(adjustedQty / float64(variant)))

		// Apply 20 cap to calculated quantity
		if calculatedQty > 20 {
			calculatedQty = 20
		}

		response[variantStr] = models.VariantCalculation{
			CalculatedQty: calculatedQty,
		}
	}

	return response, nil
}

// GetMultiSKUVariants calculates variant quantities for multiple SKUs
func (s *StockService) GetMultiSKUVariants(ctx context.Context, facility string, skuVariants map[string][]string) models.MultiSKUVariantResponse {
	response := models.MultiSKUVariantResponse{
		Data:   make(map[string]models.VariantResponse),
		Errors: make(map[string]string),
	}

	for sku, variants := range skuVariants {
		log.Printf("DEBUG: Processing SKU '%s' with variants %v", sku, variants)
		variantResponse, err := s.GetStockVariants(ctx, sku, facility, variants)
		log.Printf("DEBUG: Variant response for SKU '%s': %+v", sku, variantResponse)
		if err != nil {
			response.Errors[sku] = err.Error()
		} else {
			response.Data[sku] = variantResponse
		}
	}

	// If no successful results, remove empty Data map
	if len(response.Data) == 0 {
		response.Data = nil
	}

	// If no errors, remove empty errors map
	if len(response.Errors) == 0 {
		response.Errors = nil
	}

	return response
}
