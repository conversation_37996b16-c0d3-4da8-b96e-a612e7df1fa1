package routes

import (
	"github.com/go-redis/redis/v8"
	"github.com/gofiber/fiber/v2"
	"github.com/wms/go-redis-api/api/handlers"
	"github.com/wms/go-redis-api/services"
)

// SetupRoutes configures all application routes
func SetupRoutes(app *fiber.App, redisClient *redis.Client, tsClient *services.TypesenseClient, slack *services.SlackNotifier, healEnabled bool, safetyQuantity float64) {
	// Create handlers
	stockHandler := handlers.NewStockHandler(redisClient, tsClient, slack, healEnabled, safetyQuantity)

	// API group
	api := app.Group("/api")

	// Health check
	api.Get("/health", func(c *fiber.Ctx) error {
		return c.JSON(fiber.Map{
			"status": "ok",
		})
	})

	// Stock routes
	stock := api.Group("/stock")
	// Multi-SKU variant calculation endpoint
	stock.Get("/variants", stockHandler.GetMultiSKUVariants) // Get variant calculations for multiple SKUs
}
